// action-manager.js - Module for managing test actions

class ActionManager {
    constructor(appInstance) {
        this.app = appInstance; // Reference to the main AppiumAutomationApp instance

        // We might move DOM references here later if needed, but for now, access via this.app
        this.actionsList = document.getElementById('actionsList');
        this.actionTypeSelect = document.getElementById('actionType');
    }

    /**
     * Check if a string is an environment variable reference (env[...])
     * @param {string} value - String to check
     * @returns {boolean} - Whether the string is an environment variable reference
     */
    isEnvironmentVariable(value) {
        return typeof value === 'string' && value.startsWith('env[') && value.endsWith(']');
    }

    // --- Action Creation and Management ---

    /**
     * Add an action to the list from a loaded test case
     * @param {Object} action - The action object to add
     */
    addActionToList(action) {
        if (!action || !action.type) {
            console.error('Invalid action object:', action);
            return;
        }

        // Check if we're being called from TestCaseManager's loadTestCaseByFilename
        // In that case, the action is already in the currentActions array
        const callerName = new Error().stack.split('\n')[2]?.trim() || '';
        const isCalledFromTestCaseLoad = callerName.includes('loadTestCaseByFilename');

        if (!isCalledFromTestCaseLoad) {
            // Only add to currentActions if not called from test case loading
            this.app.currentActions.push(action);
            console.log('Added action to currentActions array');
        } else {
            console.log('Action already in currentActions array (called from test case load)');
        }

        try {
            // Get the index for the new action
            const actionIndex = isCalledFromTestCaseLoad ?
                this.app.currentActions.indexOf(action) :
                this.app.currentActions.length - 1;

            const stepNumber = actionIndex + 1;

            // Create a new list item for the action
            const actionItem = document.createElement('div');
            actionItem.className = 'list-group-item d-flex justify-content-between align-items-center action-item';
            actionItem.dataset.actionIndex = String(actionIndex);
            console.log(`Adding action at index ${actionIndex}, stored as: ${actionItem.dataset.actionIndex}`);

            // Use the getActionDescription method
            let actionText = this.getActionDescription(action);

            // Check if this is a Multi Step action
            const isMultiStep = action.type === 'multiStep';

            // Check if this is a Hook Action
            const isHookAction = action.type === 'hookAction';

            // Add special styling for Hook Actions
            if (isHookAction) {
                actionItem.classList.add('hook-action-item');
                actionItem.style.backgroundColor = '#f8f9fa'; // Light gray background
                actionItem.style.borderLeft = '4px solid #6c757d'; // Gray left border
            }

            // Create the action item with proper step number badge
            actionItem.innerHTML = `
                <div class="action-content">
                    <i class="bi bi-grip-vertical drag-indicator me-2" style="cursor:grab"></i>
                    <span class="badge bg-secondary step-number me-2">${stepNumber}</span>
                    <span class="badge ${isHookAction ? 'bg-secondary' : 'bg-primary'} me-2">${action.type}</span>
                    ${isHookAction ? '<span class="badge bg-warning text-dark me-2" title="This action will only be executed when a step fails">Recovery Action</span>' : ''}
                    ${actionText}
                    ${isMultiStep ? `
                        <button class="btn btn-sm btn-outline-secondary ms-2 toggle-multi-step" title="Expand/Collapse">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    ` : ''}
                </div>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-outline-primary play-action me-1" title="${isHookAction ? 'Hook Actions are only executed when a step fails' : 'Play this action'}" ${isHookAction ? 'disabled' : ''}>
                        <i class="bi bi-play-fill"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary edit-action me-1" title="Edit this action">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm ${(action.enabled !== false) ? 'btn-outline-warning' : 'btn-outline-success'} toggle-enabled me-1" title="${(action.enabled !== false) ? 'Disable this step' : 'Enable this step'}">
                        <i class="bi ${(action.enabled !== false) ? 'bi-pause-fill' : 'bi-play-fill'}"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info insert-above me-1" title="Insert action above">
                        <i class="bi bi-plus-circle"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info insert-below me-1" title="Insert action below">
                        <i class="bi bi-plus-circle-fill"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger delete-action" title="Delete this action">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;

            // If this is a Multi Step action, add the container for steps
            if (isMultiStep) {
                const multiStepContainer = document.createElement('div');
                multiStepContainer.className = 'multi-step-container mt-2 ps-4 border-start';
                multiStepContainer.style.display = 'none'; // Initially hidden

                // Add loading indicator
                multiStepContainer.innerHTML = `
                    <div class="text-center py-2">
                        <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                        <span class="ms-2">Loading test case steps...</span>
                    </div>
                `;

                actionItem.appendChild(multiStepContainer);

                // Add event listener for the toggle button
                const toggleButton = actionItem.querySelector('.toggle-multi-step');
                if (toggleButton) {
                    toggleButton.addEventListener('click', (e) => {
                        e.stopPropagation(); // Prevent event bubbling

                        // Toggle the container visibility
                        if (multiStepContainer.style.display === 'none') {
                            multiStepContainer.style.display = 'block';
                            toggleButton.querySelector('i').classList.replace('bi-chevron-down', 'bi-chevron-up');

                            // Load the test case steps if not already loaded
                            if (!action.steps_loaded) {
                                this.loadMultiStepTestCaseSteps(action, multiStepContainer);
                            }
                        } else {
                            multiStepContainer.style.display = 'none';
                            toggleButton.querySelector('i').classList.replace('bi-chevron-up', 'bi-chevron-down');
                        }
                    });
                }
            }

            // Add event listeners to the buttons
            const playButton = actionItem.querySelector('.play-action');
            if (playButton) {
                // Disable play button for Hook Actions
                if (action.type === 'hookAction') {
                    playButton.disabled = true;
                    playButton.title = 'Hook Actions are only executed when a step fails';
                } else {
                    playButton.disabled = false;
                    playButton.title = 'Play this action';
                }

                playButton.addEventListener('click', () => {
                    // Skip if this is a Hook Action
                    if (action.type === 'hookAction') {
                        this.app.logAction('info', 'Hook Actions are only executed when a step fails');
                        return;
                    }

                    // Get the current index from the DOM element
                    const currentIndex = Array.from(this.actionsList.children).indexOf(actionItem);
                    console.log(`Play button clicked for action at index ${currentIndex}`);

                    if (currentIndex >= 0 && currentIndex < this.app.currentActions.length) {
                        this.app.playAction(this.app.currentActions[currentIndex], currentIndex);
                    } else {
                        console.error(`Invalid action index: ${currentIndex}`);
                        this.app.logAction('error', 'Invalid action index for playing');
                    }
                });
            }

            const editButton = actionItem.querySelector('.edit-action');
            if (editButton) {
                editButton.addEventListener('click', () => {
                    // Get the current index from the DOM element
                    const currentIndex = Array.from(this.actionsList.children).indexOf(actionItem);
                    console.log(`Edit button clicked for action at index ${currentIndex}`);

                    if (currentIndex >= 0 && currentIndex < this.app.currentActions.length) {
                        this.app.editAction(this.app.currentActions[currentIndex], currentIndex);
                    } else {
                        console.error(`Invalid action index: ${currentIndex}`);
                        this.app.logAction('error', 'Invalid action index for editing');
                    }
                });
            }

            const deleteButton = actionItem.querySelector('.delete-action');
            if (deleteButton) {
                deleteButton.addEventListener('click', () => {
                    // Get the current index from the DOM element
                    const currentIndex = Array.from(this.actionsList.children).indexOf(actionItem);
                    console.log(`Delete button clicked for action at index ${currentIndex}`);

                    if (currentIndex >= 0 && currentIndex < this.app.currentActions.length) {
                        this.app.deleteAction(currentIndex);
                    } else {
                        console.error(`Invalid action index: ${currentIndex}`);
                        this.app.logAction('error', 'Invalid action index for deleting');
                    }
                });
            }

            // Set disabled state styling if action is disabled
            if (action.enabled === false) {
                actionItem.classList.add('action-disabled');
            }

            // Add to list
            if (this.actionsList) {
                this.actionsList.appendChild(actionItem);
            } else {
                console.error("ActionManager: Could not find actionsList element.");
                this.app.logAction('error', 'UI Error: Could not find the action list element.');
                return; // Stop if UI element is missing
            }

            // Update UI state
            this.app.updateExecutionButtons();
        } catch (error) {
            console.error("ActionManager Error in addActionToList:", error);
            this.app.logAction('error', `Failed to add action to UI: ${error.message}`);
        }
    }

    /**
     * Update the UI for an action item
     * @param {number} index - The index of the action in the currentActions array
     * @param {Object} action - The updated action object
     */
    updateActionItemUI(index, action) {
        try {
            // Find the action item in the DOM
            const actionsList = document.getElementById('actionsList');
            if (!actionsList) {
                console.error("ActionManager: Could not find actionsList element.");
                return;
            }

            // Find the action item with the matching index
            const actionItem = actionsList.querySelector(`[data-action-index="${index}"]`);
            if (!actionItem) {
                console.error(`ActionManager: Could not find action item with index ${index}`);
                return;
            }

            // Update the action description
            const actionText = this.getActionDescription(action);
            const actionContent = actionItem.querySelector('.action-content');
            if (actionContent) {
                // Check if this is a Multi Step action
                const isMultiStep = action.type === 'multiStep';

                // Check if this is a Hook Action
                const isHookAction = action.type === 'hookAction';

                // Add special styling for Hook Actions
                if (isHookAction) {
                    actionItem.classList.add('hook-action-item');
                    actionItem.style.backgroundColor = '#f8f9fa'; // Light gray background
                    actionItem.style.borderLeft = '4px solid #6c757d'; // Gray left border
                } else {
                    actionItem.classList.remove('hook-action-item');
                    actionItem.style.backgroundColor = '';
                    actionItem.style.borderLeft = '';
                }

                // Keep the drag indicator, step number, and type badge, but update the description
                const stepNumber = index + 1;
                actionContent.innerHTML = `
                    <i class="bi bi-grip-vertical drag-indicator me-2" style="cursor:grab"></i>
                    <span class="badge bg-secondary step-number me-2">${stepNumber}</span>
                    <span class="badge ${isHookAction ? 'bg-secondary' : 'bg-primary'} me-2">${action.type}</span>
                    ${isHookAction ? '<span class="badge bg-warning text-dark me-2" title="This action will only be executed when a step fails">Recovery Action</span>' : ''}
                    <span class="action-text">${isHookAction && action.hook_type === 'tap' && action.hook_data ?
                        `Hook Action: ${action.hook_type} (${
                            action.hook_data.method === 'locator' && action.hook_data.locator_type && action.hook_data.locator_value ?
                                `tap on element with ${action.hook_data.locator_type}: '${action.hook_data.locator_value}'` :
                            action.hook_data.method === 'image' && action.hook_data.image_filename ?
                                `tap on image '${action.hook_data.image_filename}'` :
                            action.hook_data.x !== undefined && action.hook_data.y !== undefined ?
                                `tap at coordinates (${action.hook_data.x}, ${action.hook_data.y})` :
                            'tap'
                        })` :
                        actionText
                    }</span>
                    ${isMultiStep ? `
                        <button class="btn btn-sm btn-outline-secondary ms-2 toggle-multi-step" title="Expand/Collapse">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    ` : ''}
                `;

                // If this is a Multi Step action, add the container for steps if it doesn't exist
                if (isMultiStep) {
                    let multiStepContainer = actionItem.querySelector('.multi-step-container');
                    if (!multiStepContainer) {
                        multiStepContainer = document.createElement('div');
                        multiStepContainer.className = 'multi-step-container mt-2 ps-4 border-start';
                        multiStepContainer.style.display = 'none'; // Initially hidden

                        // Add loading indicator
                        multiStepContainer.innerHTML = `
                            <div class="text-center py-2">
                                <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                <span class="ms-2">Loading test case steps...</span>
                            </div>
                        `;

                        actionItem.appendChild(multiStepContainer);
                    }

                    // Add event listener for the toggle button
                    const toggleButton = actionContent.querySelector('.toggle-multi-step');
                    if (toggleButton) {
                        toggleButton.addEventListener('click', (e) => {
                            e.stopPropagation(); // Prevent event bubbling

                            // Toggle the container visibility
                            if (multiStepContainer.style.display === 'none') {
                                multiStepContainer.style.display = 'block';
                                toggleButton.querySelector('i').classList.replace('bi-chevron-down', 'bi-chevron-up');

                                // Load the test case steps if not already loaded
                                if (!action.steps_loaded) {
                                    this.loadMultiStepTestCaseSteps(action, multiStepContainer);
                                }
                            } else {
                                multiStepContainer.style.display = 'none';
                                toggleButton.querySelector('i').classList.replace('bi-chevron-up', 'bi-chevron-down');
                            }
                        });
                    }
                }
            }

            console.log(`ActionManager: Updated UI for action at index ${index}`);
        } catch (error) {
            console.error("ActionManager Error in updateActionItemUI:", error);
            this.app.logAction('error', `Failed to update action UI: ${error.message}`);
        }
    }

    /**
     * Load the test case steps for a Multi Step action
     * @param {Object} action - The Multi Step action
     * @param {HTMLElement} container - The container to display the steps
     */
    loadMultiStepTestCaseSteps(action, container) {
        if (!action || !action.test_case_id || !container) {
            console.error('Invalid parameters for loadMultiStepTestCaseSteps');
            return;
        }

        // Show loading indicator
        container.innerHTML = `
            <div class="text-center py-2">
                <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                <span class="ms-2">Loading test case steps...</span>
            </div>
        `;

        // Check if steps are already embedded in the action
        if (action.test_case_steps && Array.isArray(action.test_case_steps) && action.test_case_steps.length > 0) {
            console.log(`Using embedded steps for multi-step action: ${action.test_case_name} (${action.test_case_steps.length} steps)`);
            action.steps_loaded = true;
            this.displayMultiStepTestCaseSteps(action, container);
            return;
        }

        // If steps are not embedded, fetch them from the server
        console.log(`Fetching steps for multi-step action: ${action.test_case_name} from ${action.test_case_id}`);
        fetch(`/api/test_cases/load/${action.test_case_id}`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.test_case) {
                    // Store the steps in the action object
                    action.test_case_steps = data.test_case.actions || [];
                    action.steps_loaded = true;

                    // Display the steps
                    this.displayMultiStepTestCaseSteps(action, container);
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            Failed to load test case steps: ${data.error || 'Unknown error'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error loading test case steps:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        Error loading test case steps: ${error.message}
                    </div>
                `;
            });
    }

    /**
     * Display the test case steps for a Multi Step action
     * @param {Object} action - The Multi Step action
     * @param {HTMLElement} container - The container to display the steps
     */
    displayMultiStepTestCaseSteps(action, container) {
        if (!action || !action.test_case_steps || !container) {
            console.error('Invalid parameters for displayMultiStepTestCaseSteps');
            return;
        }

        // Clear the container
        container.innerHTML = '';

        if (action.test_case_steps.length === 0) {
            container.innerHTML = `
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    No steps found in the test case
                </div>
            `;
            return;
        }

        // Create a list of steps
        const stepsList = document.createElement('div');
        stepsList.className = 'multi-step-list';

        // Add each step to the list
        action.test_case_steps.forEach((step, index) => {
            const stepItem = document.createElement('div');
            stepItem.className = 'multi-step-item d-flex justify-content-between align-items-center mb-1';
            stepItem.dataset.stepIndex = index;

            // Create step content
            const stepContent = document.createElement('div');
            stepContent.className = 'multi-step-content';
            stepContent.innerHTML = `
                <span class="badge bg-secondary me-2">${index + 1}</span>
                <span class="badge bg-primary me-2">${step.type}</span>
                ${this.getActionDescription(step)}
            `;

            // Create status indicator for multi-step items
            const stepStatus = document.createElement('div');
            stepStatus.className = 'multi-step-status ms-auto';
            
            // Add to step item
            stepItem.appendChild(stepContent);
            stepItem.appendChild(stepStatus);

            // Add to list
            stepsList.appendChild(stepItem);
        });

        // Add the list to the container
        container.appendChild(stepsList);
    }

    /**
     * Get a description of an action
     * @param {Object} action - The action object
     * @returns {string} - A description of the action
     */
    getActionDescription(action) {
        if (!action || !action.type) return 'Invalid action';

        // First check if the action type has a detailed description in the external function
        // This covers tap, doubleTap, swipe, text, exists, ifElseSteps, multiStep, hookAction
        if (['tap', 'doubleTap', 'swipe', 'text', 'exists', 'ifElseSteps', 'multiStep', 'hookAction'].includes(action.type)) {
            return window.getActionDescription(action);
        }

        // For other action types, use the existing switch statement
        switch (action.type) {
            case 'tapIfImageExists':
                return `Tap on image if exists: ${action.image_filename}`;
            case 'tapImage':
                return `Tap on image: ${action.image_filename}`;
            case 'tapAndType':
                if (action.method === 'coordinates') {
                    return `Tap and Type at (${action.x}, ${action.y}): "${action.text}"`;
                } else {
                    return `Tap and Type on ${action.locator_type}=${action.locator_value}: "${action.text}"`;
                }
            case 'tapOnText':
                return `Tap on Text: "${action.text_to_find}"${action.double_tap ? ' (Double Tap)' : ''}`;
            case 'wait':
                return `Wait for ${action.time || action.duration} ms`;
            case 'key':
                return `Press key: ${action.key}`;
            case 'clickElement':
                return `Click element: ${action.locator_type}=${action.locator_value}`;
            case 'waitTill':
                if (action.locator_type === 'image') {
                    return `Wait till image appears: ${action.image_filename || action.locator_value}`;
                } else if (action.locator_type === 'text') {
                    return `Wait till text appears: "${action.locator_value}"`;
                } else {
                    return `Wait till ${action.locator_type}=${action.locator_value}`;
                }
            case 'inputText':
                return `Input text in ${action.locator_type}=${action.locator_value}: ${action.text}`;
            case 'clickImage':
                return `Click image: ${action.image_path}`;
            case 'doubleClickImage':
                return `Double click image: ${action.image_path}`;
            case 'launchApp':
                return `Launch app: ${action.package_id}`;
            case 'restartApp':
                return `Restart app: ${action.package_id}`;
            case 'terminateApp':
                return `Terminate app: ${action.package_id}`;
            case 'uninstallApp':
                return `Uninstall app: ${action.package_id}`;
            case 'deviceBack':
                return 'Press device back button';
            case 'getValue':
                return `Get value from ${action.locator_type}=${action.locator_value}`;
            case 'compareValue':
                return `Compare value in ${action.locator_type}=${action.locator_value} with ${action.expected_value}`;
            case 'getParam':
                return `Get parameter: ${action.param_name}`;
            case 'setParam':
                return `Set parameter: ${action.param_name}=${action.param_value}`;
            case 'iosFunctions':
                return `iOS Function: ${action.function_name}`;
            default:
                return `${action.type} action`;
        }
    }

    addAction() {
        const addActionBtn = document.getElementById('addAction');
        const editingIndexStr = addActionBtn?.dataset.editingIndex;

        // Check if we are in edit mode
        if (editingIndexStr !== undefined) {
            const editingIndex = parseInt(editingIndexStr);
            console.log(`ActionManager: Updating action at index: ${editingIndex}`);

            if (isNaN(editingIndex) || editingIndex < 0 || editingIndex >= this.app.currentActions.length) {
                this.app.logAction('error', 'Invalid index for updating action.');
                this.app.cancelEdit(); // Reset form state on the main app
                return;
            }

            // Gather action data (similar to adding, but we will update existing obj)
            const selectedType = this.actionTypeSelect.value;
            const updatedAction = {
                 ...this.app.currentActions[editingIndex], // Keep original timestamp, potentially other meta
                 type: selectedType
             }; // Start with a copy
            let actionValid = true;

            // --- Populate updatedAction with form data (same logic as original addAction) ---
            // This uses document.getElementById directly, assuming elements are globally accessible
            switch (selectedType) {
                case 'tap':
                    // Check which tab is active
                    if (document.getElementById('tap-locator').classList.contains('active') ||
                        document.getElementById('tap-locator').classList.contains('show active')) {
                        // Using locator
                        const tapLocatorType = document.getElementById('tapLocatorType').value;
                        const tapLocatorValue = document.getElementById('tapLocatorValue').value;

                        if (!tapLocatorType || !tapLocatorValue) {
                            actionValid = false;
                            this.app.logAction('error', 'Locator type and value are required for Tap');
                            break;
                        }

                        updatedAction.locator_type = tapLocatorType;
                        updatedAction.locator_value = tapLocatorValue;
                        const tapLocatorTimeout = parseInt(document.getElementById('tapLocatorTimeout')?.value);
                        if (!isNaN(tapLocatorTimeout) && tapLocatorTimeout > 0) {
                            updatedAction.timeout = tapLocatorTimeout;
                        } else {
                            updatedAction.timeout = 60; // Default timeout increased to 60 seconds
                        }
                        const tapLocatorInterval = parseFloat(document.getElementById('tapLocatorInterval')?.value);
                        if (!isNaN(tapLocatorInterval) && tapLocatorInterval > 0) updatedAction.interval = tapLocatorInterval;
                        updatedAction.method = 'locator';
                    }
                    else if (document.getElementById('tap-image').classList.contains('active') ||
                        document.getElementById('tap-image').classList.contains('show active')) {
                        // Using image
                        const tapImage = document.getElementById('tapImageFilename').value;
                        if (!tapImage) {
                            actionValid = false;
                            this.app.logAction('error', 'Image is required for Tap by image');
                            break;
                        }
                        updatedAction.image_filename = tapImage;
                        updatedAction.threshold = parseFloat(document.getElementById('tapThreshold').value) || 0.7;
                        const tapTimeout = parseInt(document.getElementById('tapTimeout')?.value);
                        if (!isNaN(tapTimeout) && tapTimeout > 0) {
                            updatedAction.timeout = tapTimeout;
                        } else {
                            updatedAction.timeout = 60; // Default timeout increased to 60 seconds
                        }
                        updatedAction.method = 'image';
                    }
                    else {
                        // Using coordinates
                        const tapX = parseInt(document.getElementById('tapX').value);
                        const tapY = parseInt(document.getElementById('tapY').value);
                        if (isNaN(tapX) || isNaN(tapY)) {
                            actionValid = false;
                            this.app.logAction('error', 'Invalid tap coordinates');
                            break;
                        }
                        updatedAction.x = tapX;
                        updatedAction.y = tapY;
                        updatedAction.method = 'coordinates';
                    }

                    // Check if we have a fallback action
                    if (this.app.tapFallbackManager && this.app.tapFallbackManager.fallbackActionAdded) {
                        const fallbackData = this.app.tapFallbackManager.getFallbackData();
                        if (fallbackData) {
                            updatedAction.fallback_type = fallbackData.fallback_type;

                            // Add fallback data based on type
                            switch (fallbackData.fallback_type) {
                                case 'coordinates':
                                    updatedAction.fallback_x = fallbackData.x;
                                    updatedAction.fallback_y = fallbackData.y;
                                    break;

                                case 'image':
                                    updatedAction.fallback_image_filename = fallbackData.image_filename;
                                    updatedAction.fallback_threshold = fallbackData.threshold;
                                    break;

                                case 'text':
                                    updatedAction.fallback_text = fallbackData.text;
                                    break;

                                case 'locator':
                                    updatedAction.fallback_locator_type = fallbackData.locator_type;
                                    updatedAction.fallback_locator_value = fallbackData.locator_value;
                                    break;
                            }
                        }
                    }
                    break;
                case 'wait':
                    const waitTime = parseInt(document.getElementById('waitTime').value);
                    if (isNaN(waitTime) || waitTime <= 0) { actionValid = false; this.app.logAction('error', 'Invalid wait time'); break; }
                    updatedAction.time = waitTime;
                    updatedAction.duration = waitTime;
                    break;
                case 'swipe':
                    const startX = parseInt(document.getElementById('swipeStartX').value);
                    const startY = parseInt(document.getElementById('swipeStartY').value);
                    const endX = parseInt(document.getElementById('swipeEndX').value);
                    const endY = parseInt(document.getElementById('swipeEndY').value);
                    const duration = parseInt(document.getElementById('swipeDuration').value);
                    const count = parseInt(document.getElementById('swipeCount').value) || 1;
                    const interval = parseFloat(document.getElementById('swipeInterval').value) || 0.5;
                    const direction = document.getElementById('swipeDirection').value;

                    if (isNaN(startX) || isNaN(startY) || isNaN(endX) || isNaN(endY)) {
                        actionValid = false;
                        this.app.logAction('error', 'Invalid swipe coordinates');
                        break;
                    }

                    // Store coordinates as percentages (for slider values)
                    updatedAction.start_x = startX;
                    updatedAction.start_y = startY;
                    updatedAction.end_x = endX;
                    updatedAction.end_y = endY;

                    // Also store as relative coordinates in vector format for Airtest API compatibility
                    updatedAction.vector_start = [startX / 100, startY / 100]; // Convert to 0-1 range
                    updatedAction.vector_end = [endX / 100, endY / 100];      // Convert to 0-1 range

                    updatedAction.duration = (!isNaN(duration) && duration > 0) ? duration : 300;
                    updatedAction.count = count;
                    updatedAction.interval = interval;
                    updatedAction.direction = direction;
                    break;
                case 'text': // For direct text input
                    const text = document.getElementById('inputText').value;
                    if (!text) { actionValid = false; this.app.logAction('error', 'Text input cannot be empty'); break; }
                    updatedAction.text = text;

                    // Get the selected data generator
                    const inputElement = document.getElementById('inputText');
                    if (inputElement && inputElement.dataset.generatorId && inputElement.dataset.generatorId !== 'none') {
                        updatedAction.data_generator = inputElement.dataset.generatorId;
                        this.app.logAction('info', `Text will be generated using: ${inputElement.dataset.generatorId}`);
                    }
                    break;
                case 'tapOnText': // For Tap on Text action
                    const tapOnTextToFind = document.getElementById('tapOnTextToFind').value;
                    const tapOnTextTimeout = parseInt(document.getElementById('tapOnTextTimeout').value);
                    const tapOnTextDoubleTap = document.getElementById('tapOnTextDoubleTap').checked;

                    if (!tapOnTextToFind) {
                        actionValid = false;
                        this.app.logAction('error', 'Text to find is required for Tap on Text');
                        break;
                    }

                    updatedAction.text_to_find = tapOnTextToFind;
                    if (!isNaN(tapOnTextTimeout) && tapOnTextTimeout > 0) updatedAction.timeout = tapOnTextTimeout;
                    updatedAction.double_tap = tapOnTextDoubleTap;
                    break;

                case 'tapAndType': // For Tap and Type action
                    const tapAndTypeText = document.getElementById('tapAndTypeText').value;
                    const tapAndTypeTimeout = parseInt(document.getElementById('tapAndTypeTimeout').value);

                    if (!tapAndTypeText) {
                        actionValid = false;
                        this.app.logAction('error', 'Text to input is required for Tap and Type');
                        break;
                    }

                    // Check which tab is active to determine the method
                    const locatorTab = document.getElementById('tapAndType-locator-tab');
                    const coordinatesTab = document.getElementById('tapAndType-coordinates-tab');

                    // Default to locator method if we can't determine
                    let usingLocatorMethod = true;

                    // Check if coordinates tab is active
                    if (coordinatesTab && coordinatesTab.classList.contains('active')) {
                        usingLocatorMethod = false;
                    }

                    console.log('Tap and Type method detection:', {
                        locatorTabActive: locatorTab?.classList.contains('active'),
                        coordinatesTabActive: coordinatesTab?.classList.contains('active'),
                        usingLocatorMethod: usingLocatorMethod
                    });

                    if (usingLocatorMethod) {
                        // Using locator method
                        const tapAndTypeLocatorType = document.getElementById('tapAndTypeLocatorType').value;
                        const tapAndTypeLocatorValue = document.getElementById('tapAndTypeLocatorValue').value;

                        if (!tapAndTypeLocatorType || !tapAndTypeLocatorValue) {
                            actionValid = false;
                            this.app.logAction('error', 'Locator type and value are required for Tap and Type with locator method');
                            break;
                        }

                        action.method = 'locator';
                        action.locator_type = tapAndTypeLocatorType;
                        action.locator_value = tapAndTypeLocatorValue;
                    } else {
                        // Using coordinates method
                        const tapAndTypeXInput = document.getElementById('tapAndTypeX').value;
                        const tapAndTypeYInput = document.getElementById('tapAndTypeY').value;

                        // Check if values are environment variables (env[...])
                        const isXEnvVar = this.isEnvironmentVariable(tapAndTypeXInput);
                        const isYEnvVar = this.isEnvironmentVariable(tapAndTypeYInput);

                        // If either coordinate is an environment variable, keep it as a string
                        // Otherwise parse as integer
                        const tapAndTypeX = isXEnvVar ? tapAndTypeXInput : parseInt(tapAndTypeXInput);
                        const tapAndTypeY = isYEnvVar ? tapAndTypeYInput : parseInt(tapAndTypeYInput);

                        if ((isXEnvVar || !isNaN(tapAndTypeX)) && (isYEnvVar || !isNaN(tapAndTypeY))) {
                            action.method = 'coordinates';
                            action.x = tapAndTypeX;
                            action.y = tapAndTypeY;
                        } else {
                            actionValid = false;
                            this.app.logAction('error', 'Valid X and Y coordinates or environment variables are required for Tap and Type with coordinates method');
                            break;
                        }
                    }

                    action.text = tapAndTypeText;
                    if (!isNaN(tapAndTypeTimeout) && tapAndTypeTimeout > 0) action.timeout = tapAndTypeTimeout;

                    // Get the selected data generator
                    const tapAndTypeElement = document.getElementById('tapAndTypeText');
                    if (tapAndTypeElement && tapAndTypeElement.dataset.generatorId && tapAndTypeElement.dataset.generatorId !== 'none') {
                        action.data_generator = tapAndTypeElement.dataset.generatorId;
                        this.app.logAction('info', `Text will be generated using: ${tapAndTypeElement.dataset.generatorId}`);
                    }
                    break;
                case 'key': // For key events
                    const keyCode = document.getElementById('keyCode').value;
                    if (!keyCode) { actionValid = false; this.app.logAction('error', 'Key code cannot be empty'); break; }
                    updatedAction.key = keyCode;
                    break;
                case 'clickElement':
                    const clickLocatorType = document.getElementById('clickElementLocatorType').value;
                    const clickLocatorValue = document.getElementById('clickElementLocator').value;
                    if (!clickLocatorType || !clickLocatorValue) { actionValid = false; this.app.logAction('error', 'Locator type and value are required'); break; }
                    updatedAction.locator_type = clickLocatorType;
                    updatedAction.locator_value = clickLocatorValue;
                    const clickWaitBefore = parseInt(document.getElementById('clickElementWaitBefore')?.value);
                    if (!isNaN(clickWaitBefore) && clickWaitBefore > 0) updatedAction.wait_before_click = clickWaitBefore;
                    const clickTimeout = parseInt(document.getElementById('clickElementTimeout')?.value);
                    if (!isNaN(clickTimeout) && clickTimeout > 0) updatedAction.timeout = clickTimeout;
                    break;
                case 'waitTill':
                    const waitTillLocatorType = document.getElementById('waitTillLocatorType').value;
                    let waitTillLocatorValue = (waitTillLocatorType === 'image') ? document.getElementById('waitTillImage').value : document.getElementById('waitTillLocator').value;
                    if (!waitTillLocatorType || !waitTillLocatorValue) { actionValid = false; this.app.logAction('error', 'Locator type and value are required for Wait Till'); break; }
                    updatedAction.locator_type = waitTillLocatorType;
                    updatedAction.locator_value = waitTillLocatorValue;
                    const waitCondition = document.getElementById('waitTillCondition')?.value;
                    if (waitCondition) updatedAction.condition = waitCondition;
                    const waitTimeout = parseInt(document.getElementById('waitTillTimeout')?.value);
                    if (!isNaN(waitTimeout) && waitTimeout > 0) updatedAction.timeout = waitTimeout;
                    const waitInterval = parseFloat(document.getElementById('waitTillInterval')?.value);
                    if (!isNaN(waitInterval) && waitInterval > 0) updatedAction.interval = waitInterval;
                    break;
                case 'inputText': // For Input Text using locator
                    const inputLocatorType = document.getElementById('inputTextLocatorType').value;
                    const inputLocatorValue = document.getElementById('inputTextLocatorValue').value;
                    const inputTextValue = document.getElementById('inputTextValue').value; // Changed ID
                    if (!inputLocatorType || !inputLocatorValue || !inputTextValue) { actionValid = false; this.app.logAction('error', 'Locator type, value and text are required for Input Text'); break; }
                    updatedAction.locator_type = inputLocatorType;
                    updatedAction.locator_value = inputLocatorValue;
                    updatedAction.text = inputTextValue;
                    const clearFirst = document.getElementById('inputTextClearFirst')?.checked;
                    updatedAction.clear_first = clearFirst !== undefined ? clearFirst : true;
                    const inputTimeout = parseInt(document.getElementById('inputTextTimeout')?.value);
                    if (!isNaN(inputTimeout) && inputTimeout > 0) updatedAction.timeout = inputTimeout;

                    // Get the selected data generator
                    const inputTextElement = document.getElementById('inputTextValue');
                    if (inputTextElement && inputTextElement.dataset.generatorId && inputTextElement.dataset.generatorId !== 'none') {
                        updatedAction.data_generator = inputTextElement.dataset.generatorId;
                        this.app.logAction('info', `Text will be generated using: ${inputTextElement.dataset.generatorId}`);
                    }
                    break;

                 case 'clickImage':
                    const clickImagePath = document.getElementById('clickImagePath').value;
                    if (!clickImagePath) { actionValid = false; this.app.logAction('error', 'Image path is required for Click Image'); break; }
                    updatedAction.image_path = clickImagePath;
                     const clickImageThreshold = parseFloat(document.getElementById('clickImageThreshold')?.value);
                    if (!isNaN(clickImageThreshold) && clickImageThreshold > 0 && clickImageThreshold <= 1) updatedAction.threshold = clickImageThreshold;
                    const clickImageTimeoutVal = parseInt(document.getElementById('clickImageTimeout')?.value);
                    if (!isNaN(clickImageTimeoutVal) && clickImageTimeoutVal > 0) updatedAction.timeout = clickImageTimeoutVal;
                    break;
                 case 'doubleClickImage':
                     const doubleClickImagePath = document.getElementById('doubleClickImagePath').value;
                    if (!doubleClickImagePath) { actionValid = false; this.app.logAction('error', 'Image path is required for Double Click Image'); break; }
                    updatedAction.image_path = doubleClickImagePath;
                    const doubleClickImageThreshold = parseFloat(document.getElementById('doubleClickImageThreshold')?.value);
                    if (!isNaN(doubleClickImageThreshold) && doubleClickImageThreshold > 0 && doubleClickImageThreshold <= 1) updatedAction.threshold = doubleClickImageThreshold;
                     const doubleClickImageTimeout = parseInt(document.getElementById('doubleClickImageTimeout')?.value);
                    if (!isNaN(doubleClickImageTimeout) && doubleClickImageTimeout > 0) updatedAction.timeout = doubleClickImageTimeout;
                    break;
                case 'doubleTap':
                    // Check which tab is active
                    if (document.getElementById('doubletap-locator').classList.contains('active') ||
                        document.getElementById('doubletap-locator').classList.contains('show active')) {
                        // Using locator
                        const doubleTapLocatorType = document.getElementById('doubleTapLocatorType').value;
                        const doubleTapLocatorValue = document.getElementById('doubleTapLocatorValue').value;

                        // Check if we have fallback locators
                        let fallbackLocators = [];
                        if (this.app.fallbackLocatorsManager) {
                            fallbackLocators = this.app.fallbackLocatorsManager.getFallbackLocators('doubleTap');
                        }

                        // If we have no primary locator but have fallbacks, that's okay
                        if ((!doubleTapLocatorType || !doubleTapLocatorValue) && fallbackLocators.length === 0) {
                            actionValid = false;
                            this.app.logAction('error', 'Locator type and value are required for Double Tap (or at least one fallback locator)');
                            break;
                        }

                        // Add primary locator if provided
                        if (doubleTapLocatorType && doubleTapLocatorValue) {
                            updatedAction.locator_type = doubleTapLocatorType;
                            updatedAction.locator_value = doubleTapLocatorValue;
                        }

                        // Add fallback locators if any
                        if (fallbackLocators.length > 0) {
                            updatedAction.fallback_locators = fallbackLocators;
                            this.app.logAction('info', `Added ${fallbackLocators.length} fallback locators to double tap action`);
                            console.log('Fallback locators for double tap action:', JSON.stringify(fallbackLocators));
                        }

                        const doubleTapLocatorTimeout = parseInt(document.getElementById('doubleTapLocatorTimeout')?.value);
                        if (!isNaN(doubleTapLocatorTimeout) && doubleTapLocatorTimeout > 0) updatedAction.timeout = doubleTapLocatorTimeout;
                        const doubleTapLocatorInterval = parseFloat(document.getElementById('doubleTapLocatorInterval')?.value);
                        if (!isNaN(doubleTapLocatorInterval) && doubleTapLocatorInterval > 0) updatedAction.interval = doubleTapLocatorInterval;
                        updatedAction.method = 'locator';
                    }
                    else if (document.getElementById('doubletap-image').classList.contains('active') ||
                        document.getElementById('doubletap-image').classList.contains('show active')) {
                        // Using image
                        const doubleTapImage = document.getElementById('doubleTapImageFilename').value;
                        if (!doubleTapImage) {
                            actionValid = false;
                            this.app.logAction('error', 'Image is required for Double Tap by image');
                            break;
                        }
                        updatedAction.image_filename = doubleTapImage;
                        updatedAction.threshold = parseFloat(document.getElementById('doubleTapThreshold').value) || 0.7;
                        updatedAction.timeout = parseInt(document.getElementById('doubleTapTimeout').value) || 20;
                        updatedAction.method = 'image';
                    }
                    else {
                        // Using coordinates
                        const doubleTapX = parseInt(document.getElementById('doubleTapX').value);
                        const doubleTapY = parseInt(document.getElementById('doubleTapY').value);
                        if (isNaN(doubleTapX) || isNaN(doubleTapY)) {
                            actionValid = false;
                            this.app.logAction('error', 'Invalid double tap coordinates');
                            break;
                        }
                        updatedAction.x = doubleTapX;
                        updatedAction.y = doubleTapY;
                        updatedAction.method = 'coordinates';
                    }
                    break;
                 case 'launchApp':
                     const packageId = document.getElementById('appPackage').value;
                    if (!packageId) { actionValid = false; this.app.logAction('error', 'Package ID is required for Launch App'); break; }
                    updatedAction.package_id = packageId;
                    break;
                 case 'restartApp':
                    const restartPackageId = document.getElementById('restartPackage').value;
                    if (!restartPackageId) { actionValid = false; this.app.logAction('error', 'Package ID is required for Restart App'); break; }
                    updatedAction.package_id = restartPackageId;
                    break;
                case 'terminateApp':
                    const terminatePackageId = document.getElementById('terminatePackage').value;
                    if (!terminatePackageId) { actionValid = false; this.app.logAction('error', 'Package ID is required for Terminate App'); break; }
                    updatedAction.package_id = terminatePackageId;
                    break;
                case 'uninstallApp':
                    const uninstallPackageId = document.getElementById('uninstallPackage').value;
                    if (!uninstallPackageId) { actionValid = false; this.app.logAction('error', 'Package ID is required for Uninstall App'); break; }
                    updatedAction.package_id = uninstallPackageId;
                    break;


                case 'deviceBack':
                    // No parameters needed for device back action
                    break;
                case 'getValue':
                    const getValueLocatorType = document.getElementById('getValueLocatorType').value;
                    const getValueLocatorValue = document.getElementById('getValueLocatorValue').value;
                    const getValueAttribute = document.getElementById('getValueAttribute').value;
                    const getValueTimeout = parseInt(document.getElementById('getValueTimeout').value);

                    if (!getValueLocatorType || !getValueLocatorValue) {
                        actionValid = false;
                        this.app.logAction('error', 'Locator type and value are required for Get Value');
                        break;
                    }

                    updatedAction.locator_type = getValueLocatorType;
                    updatedAction.locator_value = getValueLocatorValue;
                    updatedAction.attribute = getValueAttribute;
                    if (!isNaN(getValueTimeout) && getValueTimeout > 0) updatedAction.timeout = getValueTimeout;
                    break;
                case 'compareValue':
                    const compareValueLocatorType = document.getElementById('compareValueLocatorType').value;
                    const compareValueLocatorValue = document.getElementById('compareValueLocatorValue').value;
                    const compareValueAttribute = document.getElementById('compareValueAttribute').value;
                    const compareValueExpected = document.getElementById('compareValueExpected').value;
                    const compareValueTimeout = parseInt(document.getElementById('compareValueTimeout').value);

                    if (!compareValueLocatorType || !compareValueLocatorValue) {
                        actionValid = false;
                        this.app.logAction('error', 'Locator type and value are required for Compare Value');
                        break;
                    }

                    if (compareValueExpected === undefined) {
                        actionValid = false;
                        this.app.logAction('error', 'Expected value is required for Compare Value');
                        break;
                    }

                    updatedAction.locator_type = compareValueLocatorType;
                    updatedAction.locator_value = compareValueLocatorValue;
                    updatedAction.attribute = compareValueAttribute;
                    updatedAction.expected_value = compareValueExpected;
                    if (!isNaN(compareValueTimeout) && compareValueTimeout > 0) updatedAction.timeout = compareValueTimeout;
                    break;
                case 'getParam':
                    const paramName = document.getElementById('getParamName').value;
                    if (!paramName) {
                        actionValid = false;
                        this.app.logAction('error', 'Parameter name is required for Get Parameter');
                        break;
                    }
                    updatedAction.param_name = paramName;
                    break;
                case 'setParam':
                    const setParamName = document.getElementById('setParamName').value;
                    const setParamValue = document.getElementById('setParamValue').value;
                    if (!setParamName) {
                        actionValid = false;
                        this.app.logAction('error', 'Parameter name is required for Set Parameter');
                        break;
                    }
                    if (setParamValue === undefined || setParamValue === null) {
                        actionValid = false;
                        this.app.logAction('error', 'Parameter value is required for Set Parameter');
                        break;
                    }
                    updatedAction.param_name = setParamName;
                    updatedAction.param_value = setParamValue;
                    break;
                case 'exists':
                    const existsLocatorType = document.getElementById('existsLocatorType').value;
                    let existsLocatorValue = '';

                    if (existsLocatorType === 'image') {
                        // Check if using environment variable
                        const useEnvVar = document.getElementById('existsUseEnvVar')?.checked;
                        if (useEnvVar) {
                            existsLocatorValue = document.getElementById('existsEnvVar')?.value;
                            if (!existsLocatorValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Environment variable name is required when using environment variable for image');
                                break;
                            }
                            // Mark as environment variable
                            updatedAction.use_env_var = true;
                            updatedAction.env_var_name = existsLocatorValue;
                        } else {
                            existsLocatorValue = document.getElementById('existsImage').value;
                            if (!existsLocatorValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Reference image is required for Check if Exists with Image locator');
                                break;
                            }
                        }

                        // Add threshold for image matching
                        const existsThreshold = parseFloat(document.getElementById('existsThreshold')?.value);
                        if (!isNaN(existsThreshold)) {
                            updatedAction.threshold = existsThreshold;
                        }
                    } else {
                        existsLocatorValue = document.getElementById('existsLocatorValue').value;
                        if (!existsLocatorValue) {
                            actionValid = false;
                            this.app.logAction('error', 'Locator value is required for Check if Exists');
                            break;
                        }
                    }

                    const existsTimeout = parseInt(document.getElementById('existsTimeout').value);

                    updatedAction.locator_type = existsLocatorType;
                    updatedAction.locator_value = existsLocatorValue;
                    if (!isNaN(existsTimeout) && existsTimeout > 0) updatedAction.timeout = existsTimeout;
                    break;
                case 'ifElseSteps':
                    // Get condition settings
                    const conditionType = document.getElementById('ifConditionType').value;
                    updatedAction.condition_type = conditionType;

                    // Process condition based on type
                    switch (conditionType) {
                        case 'exists':
                        case 'not_exists':
                            const ifExistsLocatorType = document.getElementById('ifExistsLocatorType').value;
                            let ifExistsLocatorValue = '';

                            if (ifExistsLocatorType === 'image') {
                                ifExistsLocatorValue = document.getElementById('ifExistsImage').value;
                                if (!ifExistsLocatorValue) {
                                    actionValid = false;
                                    this.app.logAction('error', `Reference image is required for ${conditionType === 'exists' ? 'If Exists' : 'If Not Exists'} condition with Image locator`);
                                    break;
                                }
                            } else {
                                ifExistsLocatorValue = document.getElementById('ifExistsLocatorValue').value;
                                if (!ifExistsLocatorValue) {
                                    actionValid = false;
                                    this.app.logAction('error', `Locator value is required for ${conditionType === 'exists' ? 'If Exists' : 'If Not Exists'} condition`);
                                    break;
                                }
                            }

                            const ifExistsTimeout = parseInt(document.getElementById('ifExistsTimeout').value);

                            updatedAction.condition = {
                                locator_type: ifExistsLocatorType,
                                locator_value: ifExistsLocatorValue,
                                timeout: (!isNaN(ifExistsTimeout) && ifExistsTimeout > 0) ? ifExistsTimeout : 10
                            };
                            break;

                        case 'visible':
                            const ifVisibleLocatorType = document.getElementById('ifVisibleLocatorType').value;
                            const ifVisibleLocatorValue = document.getElementById('ifVisibleLocatorValue').value;

                            if (!ifVisibleLocatorType || !ifVisibleLocatorValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Locator type and value are required for If Visible condition');
                                break;
                            }

                            const ifVisibleTimeout = parseInt(document.getElementById('ifVisibleTimeout').value);

                            updatedAction.condition = {
                                locator_type: ifVisibleLocatorType,
                                locator_value: ifVisibleLocatorValue,
                                timeout: (!isNaN(ifVisibleTimeout) && ifVisibleTimeout > 0) ? ifVisibleTimeout : 10
                            };
                            break;

                        case 'contains_text':
                            const ifContainsTextLocatorType = document.getElementById('ifContainsTextLocatorType').value;
                            const ifContainsTextLocatorValue = document.getElementById('ifContainsTextLocatorValue').value;
                            const ifContainsTextValue = document.getElementById('ifContainsTextValue').value;

                            if (!ifContainsTextLocatorType || !ifContainsTextLocatorValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Element locator type and value are required for If Contains Text condition');
                                break;
                            }

                            if (!ifContainsTextValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Text to check for is required for If Contains Text condition');
                                break;
                            }

                            const ifContainsTextTimeout = parseInt(document.getElementById('ifContainsTextTimeout').value);

                            updatedAction.condition = {
                                locator_type: ifContainsTextLocatorType,
                                locator_value: ifContainsTextLocatorValue,
                                text: ifContainsTextValue,
                                timeout: (!isNaN(ifContainsTextTimeout) && ifContainsTextTimeout > 0) ? ifContainsTextTimeout : 10
                            };
                            break;

                        case 'value_equals':
                            const ifValueEqualsLocatorType = document.getElementById('ifValueEqualsLocatorType').value;
                            const ifValueEqualsLocatorValue = document.getElementById('ifValueEqualsLocatorValue').value;
                            const ifValueEqualsExpectedValue = document.getElementById('ifValueEqualsExpectedValue').value;

                            if (!ifValueEqualsLocatorType || !ifValueEqualsLocatorValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Element locator type and value are required for If Value Equals condition');
                                break;
                            }

                            if (!ifValueEqualsExpectedValue && ifValueEqualsExpectedValue !== '') {
                                actionValid = false;
                                this.app.logAction('error', 'Expected value is required for If Value Equals condition');
                                break;
                            }

                            const ifValueEqualsTimeout = parseInt(document.getElementById('ifValueEqualsTimeout').value);

                            updatedAction.condition = {
                                locator_type: ifValueEqualsLocatorType,
                                locator_value: ifValueEqualsLocatorValue,
                                expected_value: ifValueEqualsExpectedValue,
                                timeout: (!isNaN(ifValueEqualsTimeout) && ifValueEqualsTimeout > 0) ? ifValueEqualsTimeout : 10
                            };
                            break;

                        case 'value_contains':
                            const ifValueContainsLocatorType = document.getElementById('ifValueContainsLocatorType').value;
                            const ifValueContainsLocatorValue = document.getElementById('ifValueContainsLocatorValue').value;
                            const ifValueContainsExpectedValue = document.getElementById('ifValueContainsExpectedValue').value;

                            if (!ifValueContainsLocatorType || !ifValueContainsLocatorValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Element locator type and value are required for If Value Contains condition');
                                break;
                            }

                            if (!ifValueContainsExpectedValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Text to check for is required for If Value Contains condition');
                                break;
                            }

                            const ifValueContainsTimeout = parseInt(document.getElementById('ifValueContainsTimeout').value);

                            updatedAction.condition = {
                                locator_type: ifValueContainsLocatorType,
                                locator_value: ifValueContainsLocatorValue,
                                expected_value: ifValueContainsExpectedValue,
                                timeout: (!isNaN(ifValueContainsTimeout) && ifValueContainsTimeout > 0) ? ifValueContainsTimeout : 10
                            };
                            break;

                        case 'has_attribute':
                            const ifHasAttributeLocatorType = document.getElementById('ifHasAttributeLocatorType').value;
                            const ifHasAttributeLocatorValue = document.getElementById('ifHasAttributeLocatorValue').value;
                            const ifHasAttributeName = document.getElementById('ifHasAttributeName').value;
                            const ifHasAttributeValue = document.getElementById('ifHasAttributeValue').value;

                            if (!ifHasAttributeLocatorType || !ifHasAttributeLocatorValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Element locator type and value are required for Has Attribute condition');
                                break;
                            }

                            if (!ifHasAttributeName) {
                                actionValid = false;
                                this.app.logAction('error', 'Attribute name is required for Has Attribute condition');
                                break;
                            }

                            const ifHasAttributeTimeout = parseInt(document.getElementById('ifHasAttributeTimeout').value);

                            updatedAction.condition = {
                                locator_type: ifHasAttributeLocatorType,
                                locator_value: ifHasAttributeLocatorValue,
                                attribute_name: ifHasAttributeName,
                                attribute_value: ifHasAttributeValue,
                                timeout: (!isNaN(ifHasAttributeTimeout) && ifHasAttributeTimeout > 0) ? ifHasAttributeTimeout : 10
                            };
                            break;

                        case 'screen_contains':
                            const ifScreenContainsImage = document.getElementById('ifScreenContainsImage').value;

                            if (!ifScreenContainsImage) {
                                actionValid = false;
                                this.app.logAction('error', 'Reference image is required for If Screen Contains Image condition');
                                break;
                            }

                            const ifScreenContainsThreshold = parseFloat(document.getElementById('ifScreenContainsThreshold').value);
                            const ifScreenContainsTimeout = parseInt(document.getElementById('ifScreenContainsTimeout').value);

                            updatedAction.condition = {
                                image: ifScreenContainsImage,
                                threshold: (!isNaN(ifScreenContainsThreshold) && ifScreenContainsThreshold > 0 && ifScreenContainsThreshold <= 1) ? ifScreenContainsThreshold : 0.7,
                                timeout: (!isNaN(ifScreenContainsTimeout) && ifScreenContainsTimeout > 0) ? ifScreenContainsTimeout : 10
                            };
                            break;

                        default:
                            actionValid = false;
                            this.app.logAction('error', `Condition type '${conditionType}' is not supported`);
                            break;
                    }

                    if (!actionValid) break;

                    // Get then action settings
                    const thenActionType = document.getElementById('thenActionType').value;

                    // If no action type is selected, that's okay - we'll skip the Then action
                    // This allows for "Else-only" conditions
                    if (!thenActionType) {
                        // No Then action, just continue with Else action if available
                        this.app.logAction('info', 'No Then action selected - will only execute Else action if condition is false');
                    }

                    // Only create a Then action if a type is selected
                    const thenAction = thenActionType ? { type: thenActionType } : null;

                    // Populate then action based on selected type (if one is selected)
                    if (thenActionType) {
                        switch(thenActionType) {
                        case 'tap':
                            // Check which tab is active
                            if (document.getElementById('then-tap-locator').classList.contains('active') ||
                                document.getElementById('then-tap-locator').classList.contains('show active')) {
                                // Using locator
                                const thenTapLocatorType = document.getElementById('thenTapLocatorType').value;
                                const thenTapLocatorValue = document.getElementById('thenTapLocatorValue').value;
                                if (!thenTapLocatorType || !thenTapLocatorValue) {
                                    actionValid = false;
                                    this.app.logAction('error', 'Locator type and value are required for Then Tap');
                                    break;
                                }
                                thenAction.locator_type = thenTapLocatorType;
                                thenAction.locator_value = thenTapLocatorValue;
                                const thenTapLocatorTimeout = parseInt(document.getElementById('thenTapLocatorTimeout')?.value);
                                if (!isNaN(thenTapLocatorTimeout) && thenTapLocatorTimeout > 0) thenAction.timeout = thenTapLocatorTimeout;
                                thenAction.method = 'locator';
                            }
                            else if (document.getElementById('then-tap-image').classList.contains('active') ||
                                document.getElementById('then-tap-image').classList.contains('show active')) {
                                // Using image
                                const thenTapImage = document.getElementById('thenTapImageFilename').value;
                                if (!thenTapImage) {
                                    actionValid = false;
                                    this.app.logAction('error', 'Image is required for Then Tap by image');
                                    break;
                                }
                                thenAction.image_filename = thenTapImage;
                                thenAction.threshold = parseFloat(document.getElementById('thenTapImageThreshold').value) || 0.7;
                                thenAction.timeout = parseInt(document.getElementById('thenTapImageTimeout').value) || 20;
                                thenAction.method = 'image';
                            }
                            else if (document.getElementById('then-tap-text').classList.contains('active') ||
                                document.getElementById('then-tap-text').classList.contains('show active')) {
                                // Using text
                                const thenTapText = document.getElementById('thenTapOnTextToFind').value;
                                if (!thenTapText) {
                                    actionValid = false;
                                    this.app.logAction('error', 'Text to find is required for Then Tap on Text');
                                    break;
                                }
                                thenAction.text_to_find = thenTapText;
                                thenAction.timeout = parseInt(document.getElementById('thenTapOnTextTimeout').value) || 30;
                                thenAction.method = 'text';
                            }
                            else {
                                // Using coordinates
                                const thenTapX = parseInt(document.getElementById('thenTapX').value);
                                const thenTapY = parseInt(document.getElementById('thenTapY').value);
                                if (isNaN(thenTapX) || isNaN(thenTapY)) {
                                    actionValid = false;
                                    this.app.logAction('error', 'Invalid tap coordinates for Then action');
                                    break;
                                }
                                thenAction.x = thenTapX;
                                thenAction.y = thenTapY;
                                thenAction.method = 'coordinates';
                            }
                            break;

                        case 'doubleTap':
                            const thenDoubleTapX = parseInt(document.getElementById('thenDoubleTapX').value);
                            const thenDoubleTapY = parseInt(document.getElementById('thenDoubleTapY').value);
                            if (isNaN(thenDoubleTapX) || isNaN(thenDoubleTapY)) {
                                actionValid = false;
                                this.app.logAction('error', 'Invalid double tap coordinates for Then action');
                                break;
                            }
                            thenAction.x = thenDoubleTapX;
                            thenAction.y = thenDoubleTapY;
                            break;

                        case 'swipe':
                            const thenSwipeStartX = parseInt(document.getElementById('thenSwipeStartX').value);
                            const thenSwipeStartY = parseInt(document.getElementById('thenSwipeStartY').value);
                            const thenSwipeEndX = parseInt(document.getElementById('thenSwipeEndX').value);
                            const thenSwipeEndY = parseInt(document.getElementById('thenSwipeEndY').value);
                            const thenSwipeDuration = parseInt(document.getElementById('thenSwipeDuration').value);

                            if (isNaN(thenSwipeStartX) || isNaN(thenSwipeStartY) || isNaN(thenSwipeEndX) || isNaN(thenSwipeEndY)) {
                                actionValid = false;
                                this.app.logAction('error', 'Invalid swipe coordinates for Then action');
                                break;
                            }

                            thenAction.start_x = thenSwipeStartX;
                            thenAction.start_y = thenSwipeStartY;
                            thenAction.end_x = thenSwipeEndX;
                            thenAction.end_y = thenSwipeEndY;
                            thenAction.duration = (!isNaN(thenSwipeDuration) && thenSwipeDuration > 0) ? thenSwipeDuration : 300;
                            break;

                        case 'text':
                            const thenInputText = document.getElementById('thenInputText').value;
                            if (!thenInputText) {
                                actionValid = false;
                                this.app.logAction('error', 'Text input cannot be empty for Then action');
                                break;
                            }
                            thenAction.text = thenInputText;
                            break;

                        case 'key':
                            const thenKeyCode = document.getElementById('thenKeyCode').value;
                            if (!thenKeyCode) {
                                actionValid = false;
                                this.app.logAction('error', 'Key code cannot be empty for Then action');
                                break;
                            }
                            thenAction.key = thenKeyCode;
                            break;

                        case 'wait':
                            const thenWaitTime = parseInt(document.getElementById('thenWaitTime').value);
                            if (isNaN(thenWaitTime) || thenWaitTime <= 0) {
                                actionValid = false;
                                this.app.logAction('error', 'Invalid wait time for Then action');
                                break;
                            }
                            thenAction.time = thenWaitTime;
                            break;



                        case 'clickElement':
                            const thenClickLocatorType = document.getElementById('thenClickElementLocatorType').value;
                            const thenClickLocatorValue = document.getElementById('thenClickElementLocator').value;
                            if (!thenClickLocatorType || !thenClickLocatorValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Locator type and value are required for Then Click Element action');
                                break;
                            }
                            thenAction.locator_type = thenClickLocatorType;
                            thenAction.locator_value = thenClickLocatorValue;
                            const thenClickTimeout = parseInt(document.getElementById('thenClickElementTimeout').value);
                            if (!isNaN(thenClickTimeout) && thenClickTimeout > 0) thenAction.timeout = thenClickTimeout;
                            break;

                        case 'waitTill':
                            const thenWaitTillLocatorType = document.getElementById('thenWaitTillLocatorType').value;
                            const thenWaitTillLocatorValue = document.getElementById('thenWaitTillLocator').value;
                            if (!thenWaitTillLocatorType || !thenWaitTillLocatorValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Locator type and value are required for Then Wait Till action');
                                break;
                            }
                            thenAction.locator_type = thenWaitTillLocatorType;
                            thenAction.locator_value = thenWaitTillLocatorValue;
                            const thenWaitTillTimeout = parseInt(document.getElementById('thenWaitTillTimeout').value);
                            if (!isNaN(thenWaitTillTimeout) && thenWaitTillTimeout > 0) thenAction.timeout = thenWaitTillTimeout;
                            break;

                        case 'swipeTillVisible':
                            const thenSwipeTVDirection = document.getElementById('thenSwipeTillVisibleDirection').value;
                            const thenSwipeTVCount = parseInt(document.getElementById('thenSwipeTillVisibleCount').value);

                            thenAction.direction = thenSwipeTVDirection;
                            thenAction.count = (!isNaN(thenSwipeTVCount) && thenSwipeTVCount > 0) ? thenSwipeTVCount : 5;
                            break;

                        case 'textClear':
                            const thenTextClearInput = document.getElementById('thenTextClearInput').value;
                            if (!thenTextClearInput) {
                                actionValid = false;
                                this.app.logAction('error', 'Text input cannot be empty for Then Clear & Input Text action');
                                break;
                            }
                            thenAction.text = thenTextClearInput;
                            const thenTextClearDelay = parseInt(document.getElementById('thenTextClearDelay').value);
                            if (!isNaN(thenTextClearDelay) && thenTextClearDelay > 0) thenAction.delay = thenTextClearDelay;
                            break;

                        case 'iosFunctions':
                            const thenIosFunction = document.getElementById('thenIosFunction').value;
                            if (!thenIosFunction) {
                                actionValid = false;
                                this.app.logAction('error', 'iOS function must be selected for Then action');
                                break;
                            }
                            thenAction.function_name = thenIosFunction;

                            // Add function-specific parameters
                            if (thenIosFunction === 'alert_click') {
                                const thenButtonText = document.getElementById('thenIosAlertButtonText').value;
                                if (!thenButtonText) {
                                    actionValid = false;
                                    this.app.logAction('error', 'Button text is required for Then alert_click function');
                                    break;
                                }
                                thenAction.button_text = thenButtonText;
                            } else if (thenIosFunction === 'alert_wait') {
                                const thenTimeout = parseInt(document.getElementById('thenIosAlertTimeout').value);
                                if (!isNaN(thenTimeout) && thenTimeout > 0) {
                                    thenAction.timeout = thenTimeout;
                                } else {
                                    thenAction.timeout = 2; // Default timeout
                                }
                            } else if (thenIosFunction === 'set_clipboard') {
                                const thenContent = document.getElementById('thenIosClipboardContent').value;
                                if (!thenContent) {
                                    actionValid = false;
                                    this.app.logAction('error', 'Content is required for Then set_clipboard function');
                                    break;
                                }
                                thenAction.content = thenContent;
                            }
                            break;

                        default:
                            actionValid = false;
                            this.app.logAction('error', `Then action type '${thenActionType}' is not supported`);
                            break;
                    }

                    if (!actionValid) break;
                    }  // Close the if (thenActionType) block

                    updatedAction.then_action = thenAction;

                    // No else action section anymore
                    break;
                case 'doubleClickImageAirtest':
                    const imageName = document.getElementById('doubleClickAirtestImageFilename').value;
                    if (!imageName) { actionValid = false; this.app.logAction('error', 'Image filename is required'); break; }
                    updatedAction.image_filename = imageName;
                    const threshold = parseFloat(document.getElementById('doubleClickAirtestThreshold')?.value);
                    if (!isNaN(threshold) && threshold > 0 && threshold <= 1) updatedAction.threshold = threshold;
                    const timeout = parseInt(document.getElementById('doubleClickAirtestTimeout')?.value);
                    if (!isNaN(timeout) && timeout > 0) updatedAction.timeout = timeout;
                    break;
                case 'doubleTap':
                    // Check which tab is active
                    if (document.getElementById('doubletap-locator').classList.contains('active') ||
                        document.getElementById('doubletap-locator').classList.contains('show active')) {
                        // Using locator
                        const doubleTapLocatorType = document.getElementById('doubleTapLocatorType').value;
                        const doubleTapLocatorValue = document.getElementById('doubleTapLocatorValue').value;

                        // Check if we have fallback locators
                        let fallbackLocators = [];
                        if (this.app.fallbackLocatorsManager) {
                            fallbackLocators = this.app.fallbackLocatorsManager.getFallbackLocators('doubleTap');
                        }

                        // If we have no primary locator but have fallbacks, that's okay
                        if ((!doubleTapLocatorType || !doubleTapLocatorValue) && fallbackLocators.length === 0) {
                            actionValid = false;
                            this.app.logAction('error', 'Locator type and value are required for Double Tap (or at least one fallback locator)');
                            break;
                        }

                        // Add primary locator if provided
                        if (doubleTapLocatorType && doubleTapLocatorValue) {
                            updatedAction.locator_type = doubleTapLocatorType;
                            updatedAction.locator_value = doubleTapLocatorValue;
                        }

                        // Add fallback locators if any
                        if (fallbackLocators.length > 0) {
                            updatedAction.fallback_locators = fallbackLocators;
                            this.app.logAction('info', `Added ${fallbackLocators.length} fallback locators to double tap action`);
                            console.log('Fallback locators for double tap action:', JSON.stringify(fallbackLocators));
                        }

                        const doubleTapLocatorTimeout = parseInt(document.getElementById('doubleTapLocatorTimeout')?.value);
                        if (!isNaN(doubleTapLocatorTimeout) && doubleTapLocatorTimeout > 0) updatedAction.timeout = doubleTapLocatorTimeout;
                        const doubleTapLocatorInterval = parseFloat(document.getElementById('doubleTapLocatorInterval')?.value);
                        if (!isNaN(doubleTapLocatorInterval) && doubleTapLocatorInterval > 0) updatedAction.interval = doubleTapLocatorInterval;
                        updatedAction.method = 'locator';
                    }
                    else if (document.getElementById('doubletap-image').classList.contains('active') ||
                        document.getElementById('doubletap-image').classList.contains('show active')) {
                        // Using image
                        const doubleTapImage = document.getElementById('doubleTapImageFilename').value;
                        if (!doubleTapImage) {
                            actionValid = false;
                            this.app.logAction('error', 'Image is required for Double Tap by image');
                            break;
                        }
                        updatedAction.image_filename = doubleTapImage;
                        updatedAction.threshold = parseFloat(document.getElementById('doubleTapThreshold').value) || 0.7;
                        updatedAction.timeout = parseInt(document.getElementById('doubleTapTimeout').value) || 20;
                        updatedAction.method = 'image';
                    }
                    else {
                        // Using coordinates
                        const doubleTapX = parseInt(document.getElementById('doubleTapX').value);
                        const doubleTapY = parseInt(document.getElementById('doubleTapY').value);
                        if (isNaN(doubleTapX) || isNaN(doubleTapY)) {
                            actionValid = false;
                            this.app.logAction('error', 'Invalid double tap coordinates');
                            break;
                        }
                        updatedAction.x = doubleTapX;
                        updatedAction.y = doubleTapY;
                        updatedAction.method = 'coordinates';
                    }
                    break;
                case 'swipeTillVisible':
                    const swipeTVDirection = document.getElementById('swipeTillVisibleDirection').value;
                    const startXTV = parseInt(document.getElementById('swipeTillVisibleStartX').value);
                    const startYTV = parseInt(document.getElementById('swipeTillVisibleStartY').value);
                    const endXTV = parseInt(document.getElementById('swipeTillVisibleEndX').value);
                    const endYTV = parseInt(document.getElementById('swipeTillVisibleEndY').value);
                    const durationTV = parseInt(document.getElementById('swipeTillVisibleDuration').value);
                    const countTV = parseInt(document.getElementById('swipeTillVisibleCount').value) || 1;
                    const intervalTV = parseFloat(document.getElementById('swipeTillVisibleInterval').value) || 0.5;
                    let locatorType = document.getElementById('swipeTillVisibleLocatorType').value;
                    let locatorValue = '';
                    let textToFind = '';

                    // Handle text locator type
                    if (locatorType === 'text') {
                        textToFind = document.getElementById('swipeTillVisibleTextToFind').value;
                        if (!textToFind) {
                            actionValid = false;
                            this.app.logAction('error', 'Text to find is required for Swipe Till Visible with Text locator');
                            break;
                        }
                        updatedAction.text_to_find = textToFind;
                    } else {
                        // For other locator types, get the locator value
                        locatorValue = document.getElementById('swipeTillVisibleLocatorValue').value;
                        if (!locatorValue && locatorType !== 'text') {
                            // Only require locator value if not using text or image
                            const referenceImage = document.getElementById('swipeTillVisibleReferenceImage').value;
                            if (!referenceImage || referenceImage.trim() === '') {
                                actionValid = false;
                                this.app.logAction('error', 'Either locator value or reference image is required for Swipe Till Visible');
                                break;
                            }
                        }
                        updatedAction.locator_value = locatorValue;
                    }

                    // Get reference image (optional)
                    const referenceImage = document.getElementById('swipeTillVisibleReferenceImage').value;
                    if (referenceImage && referenceImage.trim() !== '') {
                        updatedAction.image_filename = referenceImage;

                        // Add threshold and timeout for image recognition
                        const imageThreshold = parseFloat(document.getElementById('swipeTillVisibleThreshold')?.value);
                        if (!isNaN(imageThreshold) && imageThreshold > 0 && imageThreshold <= 1) {
                            updatedAction.threshold = imageThreshold;
                        } else {
                            updatedAction.threshold = 0.7; // Default
                        }

                        const imageTimeout = parseInt(document.getElementById('swipeTillVisibleImageTimeout')?.value);
                        if (!isNaN(imageTimeout) && imageTimeout > 0) {
                            updatedAction.timeout = imageTimeout;
                        } else {
                            updatedAction.timeout = 20; // Default
                        }
                    }

                    // Always include locator type
                    updatedAction.locator_type = locatorType;

                    if (isNaN(startXTV) || isNaN(startYTV) || isNaN(endXTV) || isNaN(endYTV)) {
                        actionValid = false;
                        this.app.logAction('error', 'Invalid swipeTillVisible coordinates');
                        break;
                    }
                    updatedAction.start_x = startXTV;
                    updatedAction.start_y = startYTV;
                    updatedAction.end_x = endXTV;
                    updatedAction.end_y = endYTV;
                    updatedAction.vector_start = [startXTV / 100, startYTV / 100];
                    updatedAction.vector_end = [endXTV / 100, endYTV / 100];
                    updatedAction.duration = (!isNaN(durationTV) && durationTV > 0) ? durationTV : 300;
                    updatedAction.count = countTV;
                    updatedAction.interval = intervalTV;
                    updatedAction.direction = swipeTVDirection;
                    break;
                case 'textClear':
                    const textClearInput = document.getElementById('textClearInput').value;
                    if (!textClearInput) {
                        actionValid = false;
                        this.app.logAction('error', 'Text input cannot be empty for Clear & Input Text');
                        break;
                    }
                    updatedAction.text = textClearInput;

                    // Get delay between clear and input (milliseconds)
                    const textClearDelay = parseInt(document.getElementById('textClearDelay').value);
                    if (!isNaN(textClearDelay) && textClearDelay > 0) {
                        updatedAction.delay = textClearDelay;
                    } else {
                        updatedAction.delay = 500; // Default delay
                    }
                    break;
                case 'iosFunctions':
                    const iosFunction = document.getElementById('iosFunction').value;
                    if (!iosFunction) {
                        actionValid = false;
                        this.app.logAction('error', 'iOS function must be selected');
                        break;
                    }
                    updatedAction.function_name = iosFunction;

                    // Add function-specific parameters
                    if (iosFunction === 'alert_click') {
                        const buttonText = document.getElementById('iosAlertButtonText').value;
                        if (!buttonText) {
                            actionValid = false;
                            this.app.logAction('error', 'Button text is required for alert_click function');
                            break;
                        }
                        updatedAction.button_text = buttonText;
                    } else if (iosFunction === 'alert_wait') {
                        const timeout = parseInt(document.getElementById('iosAlertTimeout').value);
                        if (!isNaN(timeout) && timeout > 0) {
                            updatedAction.timeout = timeout;
                        } else {
                            updatedAction.timeout = 2; // Default timeout
                        }
                    } else if (iosFunction === 'set_clipboard') {
                        const content = document.getElementById('iosClipboardContent').value;
                        if (!content) {
                            actionValid = false;
                            this.app.logAction('error', 'Content is required for set_clipboard function');
                            break;
                        }
                        updatedAction.content = content;
                    }
                    break;
                 // Add other action types as needed
                 default:
                    console.warn(`ActionManager: Update logic not fully implemented for action type: ${selectedType}`);
                    actionValid = false; // Mark as invalid if type not fully handled
                    this.app.logAction('error', `Action type '${selectedType}' is not fully configured for updates.`);
                    break;
            }
            // --- End of populating updatedAction ---

            if (actionValid) {
                // Update the action in the main app's array
                this.app.currentActions[editingIndex] = updatedAction;

                // Update the UI list item (Calls main app method)
                this.updateActionItemUI(editingIndex, updatedAction);

                this.app.logAction('success', `Updated action #${editingIndex + 1}`);
                this.app.cancelEdit(); // Reset button and form on main app

            } else {
                 this.app.logAction('warning', 'Update cancelled due to invalid form data.');
                 // Optionally provide more specific feedback
            }
            return; // Exit addAction after handling update
        }

        // --- Original logic for adding a NEW action ---
        const selectedType = this.actionTypeSelect.value;
        console.log(`ActionManager: Adding action of type: ${selectedType}`);

        // Debug log for tapAndType
        if (selectedType === 'tapAndType') {
            console.log('TapAndType form state:', {
                locatorTab: document.getElementById('tapAndType-locator'),
                coordinatesTab: document.getElementById('tapAndType-coordinates'),
                locatorTabActive: document.getElementById('tapAndType-locator')?.classList.contains('active'),
                coordinatesTabActive: document.getElementById('tapAndType-coordinates')?.classList.contains('active'),
                locatorType: document.getElementById('tapAndTypeLocatorType')?.value,
                locatorValue: document.getElementById('tapAndTypeLocatorValue')?.value,
                x: document.getElementById('tapAndTypeX')?.value,
                y: document.getElementById('tapAndTypeY')?.value,
                text: document.getElementById('tapAndTypeText')?.value
            });
        }

        // Create the base action object
        const action = {
            type: selectedType,
            timestamp: Date.now() // Add timestamp here
        };

        // Define updatedAction for non-edit case to avoid reference errors
        const updatedAction = action;

        let actionValid = true;

        // Add type-specific properties (This is the same switch as above, should be refactored later)
        switch (selectedType) {
            case 'tapIfImageExists':
                const tapIfImageExistsFilename = document.getElementById('tapIfImageExistsFilename').value;
                if (!tapIfImageExistsFilename) {
                    actionValid = false;
                    this.app.logAction('error', 'Reference image is required for Tap If Image Exists');
                    break;
                }
                action.image_filename = tapIfImageExistsFilename;
                action.threshold = parseFloat(document.getElementById('tapIfImageExistsThreshold').value) || 0.7;
                action.timeout = parseInt(document.getElementById('tapIfImageExistsTimeout').value) || 5;
                break;

            case 'tapImage':
                const tapImageFilename = document.getElementById('tapImageFilename').value;
                if (!tapImageFilename) {
                    actionValid = false;
                    this.app.logAction('error', 'Reference image is required for Tap Image');
                    break;
                }
                action.image_filename = tapImageFilename;
                action.threshold = parseFloat(document.getElementById('tapThreshold').value) || 0.7;
                action.timeout = parseInt(document.getElementById('tapTimeout').value) || 20;
                action.method = 'image';
                break;

             case 'tap':
                // Check which tab is active
                if (document.getElementById('tap-locator').classList.contains('active') ||
                    document.getElementById('tap-locator').classList.contains('show active')) {
                    // Using locator
                    const tapLocatorType = document.getElementById('tapLocatorType').value;
                    const tapLocatorValue = document.getElementById('tapLocatorValue').value;
                    if (!tapLocatorType || !tapLocatorValue) {
                        actionValid = false;
                        this.app.logAction('error', 'Locator type and value are required for Tap');
                        break;
                    }
                    action.locator_type = tapLocatorType;
                    action.locator_value = tapLocatorValue;
                    const tapLocatorTimeout = parseInt(document.getElementById('tapLocatorTimeout')?.value);
                    if (!isNaN(tapLocatorTimeout) && tapLocatorTimeout > 0) {
                        action.timeout = tapLocatorTimeout;
                    } else {
                        action.timeout = 60; // Default timeout increased to 60 seconds
                    }
                    const tapLocatorInterval = parseFloat(document.getElementById('tapLocatorInterval')?.value);
                    if (!isNaN(tapLocatorInterval) && tapLocatorInterval > 0) action.interval = tapLocatorInterval;
                    action.method = 'locator';
                }
                else if (document.getElementById('tap-image').classList.contains('active') ||
                    document.getElementById('tap-image').classList.contains('show active')) {
                    // Using image
                    // Check if using text input or dropdown
                    const useTextInput = document.getElementById('tapImageUseText').checked;
                    let imageFilename;
                    
                    if (useTextInput) {
                        imageFilename = document.getElementById('tapImageTextInput').value.trim();
                    } else {
                        imageFilename = document.getElementById('tapImageFilename').value;
                    }
                    
                    if (!imageFilename) {
                        actionValid = false;
                        this.app.logAction('error', 'Image is required for Tap by image');
                        break;
                    }
                    
                    // Always consider environment variables valid
                    if (this.isEnvironmentVariable(imageFilename)) {
                        actionValid = true;
                    }
                    action.image_filename = imageFilename;
                    action.threshold = parseFloat(document.getElementById('tapThreshold').value) || 0.7;
                    const tapTimeout = parseInt(document.getElementById('tapTimeout')?.value);
                    if (!isNaN(tapTimeout) && tapTimeout > 0) {
                        action.timeout = tapTimeout;
                    } else {
                        action.timeout = 60; // Default timeout increased to 60 seconds
                    }
                    action.method = 'image';
                }
                else {
                    // Using coordinates
                    const tapX = parseInt(document.getElementById('tapX').value);
                    const tapY = parseInt(document.getElementById('tapY').value);
                    if (isNaN(tapX) || isNaN(tapY)) {
                        actionValid = false;
                        this.app.logAction('error', 'Invalid tap coordinates');
                        break;
                    }
                    action.x = tapX;
                    action.y = tapY;
                    action.method = 'coordinates';
                }

                // Check if we have a fallback action
                if (this.app.tapFallbackManager && this.app.tapFallbackManager.fallbackActionAdded) {
                    const fallbackData = this.app.tapFallbackManager.getFallbackData();
                    if (fallbackData) {
                        action.fallback_type = fallbackData.fallback_type;

                        // Add fallback data based on type
                        switch (fallbackData.fallback_type) {
                            case 'coordinates':
                                action.fallback_x = fallbackData.x;
                                action.fallback_y = fallbackData.y;
                                break;

                            case 'image':
                                action.fallback_image_filename = fallbackData.image_filename;
                                action.fallback_threshold = fallbackData.threshold;
                                break;

                            case 'text':
                                action.fallback_text = fallbackData.text;
                                break;

                            case 'locator':
                                action.fallback_locator_type = fallbackData.locator_type;
                                action.fallback_locator_value = fallbackData.locator_value;
                                break;
                        }
                    }
                }
                break;
            case 'wait':
                const waitTime = parseInt(document.getElementById('waitTime').value);
                if (isNaN(waitTime) || waitTime <= 0) { actionValid = false; this.app.logAction('error', 'Invalid wait time'); break; }
                action.time = waitTime;
                action.duration = waitTime;
                break;
            case 'swipe':
                const startX = parseInt(document.getElementById('swipeStartX').value);
                const startY = parseInt(document.getElementById('swipeStartY').value);
                const endX = parseInt(document.getElementById('swipeEndX').value);
                const endY = parseInt(document.getElementById('swipeEndY').value);
                const duration = parseInt(document.getElementById('swipeDuration').value);
                const count = parseInt(document.getElementById('swipeCount').value) || 1;
                const interval = parseFloat(document.getElementById('swipeInterval').value) || 0.5;
                const direction = document.getElementById('swipeDirection').value;

                if (isNaN(startX) || isNaN(startY) || isNaN(endX) || isNaN(endY)) {
                    actionValid = false;
                    this.app.logAction('error', 'Invalid swipe coordinates');
                    break;
                }

                // Store coordinates as percentages (for slider values)
                action.start_x = startX;
                action.start_y = startY;
                action.end_x = endX;
                action.end_y = endY;

                // Also store as relative coordinates in vector format for Airtest API compatibility
                action.vector_start = [startX / 100, startY / 100]; // Convert to 0-1 range
                action.vector_end = [endX / 100, endY / 100];      // Convert to 0-1 range

                action.duration = (!isNaN(duration) && duration > 0) ? duration : 300;
                action.count = count;
                action.interval = interval;
                action.direction = direction;
                break;
            case 'text':
                const text = document.getElementById('inputText').value;
                if (!text) { actionValid = false; this.app.logAction('error', 'Text input cannot be empty'); break; }
                action.text = text;
                break;
            case 'tapOnText':
                const tapOnTextToFind = document.getElementById('tapOnTextToFind').value;
                const tapOnTextTimeout = parseInt(document.getElementById('tapOnTextTimeout').value);
                const tapOnTextDoubleTap = document.getElementById('tapOnTextDoubleTap').checked;

                if (!tapOnTextToFind) {
                    actionValid = false;
                    this.app.logAction('error', 'Text to find is required for Tap on Text');
                    break;
                }

                action.text_to_find = tapOnTextToFind;
                if (!isNaN(tapOnTextTimeout) && tapOnTextTimeout > 0) action.timeout = tapOnTextTimeout;
                if (tapOnTextDoubleTap) action.double_tap = true;
                break;

            case 'key':
                const keyCode = document.getElementById('keyCode').value;
                if (!keyCode) { actionValid = false; this.app.logAction('error', 'Key code cannot be empty'); break; }
                action.key = keyCode;
                break;
            case 'clickElement':
                const clickLocatorType = document.getElementById('clickElementLocatorType').value;
                const clickLocatorValue = document.getElementById('clickElementLocator').value;
                if (!clickLocatorType || !clickLocatorValue) { actionValid = false; this.app.logAction('error', 'Locator type and value are required'); break; }
                action.locator_type = clickLocatorType;
                action.locator_value = clickLocatorValue;
                const clickWaitBefore = parseInt(document.getElementById('clickElementWaitBefore')?.value);
                if (!isNaN(clickWaitBefore) && clickWaitBefore > 0) action.wait_before_click = clickWaitBefore;
                const clickTimeout = parseInt(document.getElementById('clickElementTimeout')?.value);
                if (!isNaN(clickTimeout) && clickTimeout > 0) action.timeout = clickTimeout;
                break;
            case 'waitTill':
                const waitTillLocatorType = document.getElementById('waitTillLocatorType').value;

                if (waitTillLocatorType === 'image') {
                    // Handle image locator type
                    const waitTillImage = document.getElementById('waitTillImage').value;
                    if (!waitTillImage) {
                        actionValid = false;
                        this.app.logAction('error', 'Reference image is required for Wait Till Element with Image locator');
                        break;
                    }
                    
                    // Always consider environment variables valid
                    if (this.isEnvironmentVariable(waitTillImage)) {
                        actionValid = true;
                    }
                    action.locator_type = 'image';
                    action.locator_value = waitTillImage;
                    action.image_filename = waitTillImage;

                    // Get threshold for image matching
                    const waitTillThreshold = parseFloat(document.getElementById('waitTillThreshold')?.value);
                    if (!isNaN(waitTillThreshold) && waitTillThreshold > 0 && waitTillThreshold <= 1) {
                        action.threshold = waitTillThreshold;
                    } else {
                        action.threshold = 0.7; // Default threshold
                    }
                } else {
                    // Handle other locator types
                    const waitTillLocatorValue = document.getElementById('waitTillLocator').value;
                    if (!waitTillLocatorType || !waitTillLocatorValue) {
                        actionValid = false;
                        this.app.logAction('error', 'Locator type and value are required for Wait Till Element');
                        break;
                    }
                    action.locator_type = waitTillLocatorType;
                    action.locator_value = waitTillLocatorValue;

                    // For text locator type, add additional properties
                    if (waitTillLocatorType === 'text') {
                        // No additional properties needed, the backend will handle text detection
                        // Just log for clarity
                        console.log(`Wait Till Element with Text locator: '${waitTillLocatorValue}'`);
                    }
                }

                const waitCondition = document.getElementById('waitTillCondition')?.value;
                if (waitCondition) action.condition = waitCondition;

                const waitTimeout = parseInt(document.getElementById('waitTillTimeout')?.value);
                if (!isNaN(waitTimeout) && waitTimeout > 0) action.timeout = waitTimeout;

                const waitInterval = parseFloat(document.getElementById('waitTillInterval')?.value);
                if (!isNaN(waitInterval) && waitInterval > 0) action.interval = waitInterval;
                break;
            case 'inputText': // For Input Text using locator
                const inputLocatorType = document.getElementById('inputTextLocatorType').value;
                const inputLocatorValue = document.getElementById('inputTextLocatorValue').value;
                const inputTextValue = document.getElementById('inputTextValue').value; // Changed ID
                if (!inputLocatorType || !inputLocatorValue || !inputTextValue) { actionValid = false; this.app.logAction('error', 'Locator type, value and text are required for Input Text'); break; }
                action.locator_type = inputLocatorType;
                action.locator_value = inputLocatorValue;
                action.text = inputTextValue;
                const clearFirst = document.getElementById('inputTextClearFirst')?.checked;
                action.clear_first = clearFirst !== undefined ? clearFirst : true;
                const inputTimeout = parseInt(document.getElementById('inputTextTimeout')?.value);
                if (!isNaN(inputTimeout) && inputTimeout > 0) action.timeout = inputTimeout;
                break;

            case 'clickImage':
                const clickImagePath = document.getElementById('clickImagePath').value;
                if (!clickImagePath) { actionValid = false; this.app.logAction('error', 'Image path is required for Click Image'); break; }
                action.image_path = clickImagePath;
                const clickImageThreshold = parseFloat(document.getElementById('clickImageThreshold')?.value);
                if (!isNaN(clickImageThreshold) && clickImageThreshold > 0 && clickImageThreshold <= 1) action.threshold = clickImageThreshold;
                const clickImageTimeoutVal = parseInt(document.getElementById('clickImageTimeout')?.value);
                if (!isNaN(clickImageTimeoutVal) && clickImageTimeoutVal > 0) action.timeout = clickImageTimeoutVal;
                break;
            case 'doubleClickImage':
                const doubleClickImagePath = document.getElementById('doubleClickImagePath').value;
                if (!doubleClickImagePath) { actionValid = false; this.app.logAction('error', 'Image path is required for Double Click Image'); break; }
                action.image_path = doubleClickImagePath;
                const doubleClickImageThreshold = parseFloat(document.getElementById('doubleClickImageThreshold')?.value);
                if (!isNaN(doubleClickImageThreshold) && doubleClickImageThreshold > 0 && doubleClickImageThreshold <= 1) action.threshold = doubleClickImageThreshold;
                const doubleClickImageTimeout = parseInt(document.getElementById('doubleClickImageTimeout')?.value);
                if (!isNaN(doubleClickImageTimeout) && doubleClickImageTimeout > 0) action.timeout = doubleClickImageTimeout;
                break;
            case 'doubleTap':
                // Check which tab is active
                if (document.getElementById('doubletap-locator').classList.contains('active') ||
                    document.getElementById('doubletap-locator').classList.contains('show active')) {
                    // Using locator
                    const doubleTapLocatorType = document.getElementById('doubleTapLocatorType').value;
                    const doubleTapLocatorValue = document.getElementById('doubleTapLocatorValue').value;

                    // Check if we have fallback locators
                    let fallbackLocators = [];
                    if (this.app.fallbackLocatorsManager) {
                        fallbackLocators = this.app.fallbackLocatorsManager.getFallbackLocators('doubleTap');
                    }

                    // If we have no primary locator but have fallbacks, that's okay
                    if ((!doubleTapLocatorType || !doubleTapLocatorValue) && fallbackLocators.length === 0) {
                        actionValid = false;
                        this.app.logAction('error', 'Locator type and value are required for Double Tap (or at least one fallback locator)');
                        break;
                    }

                    // Add primary locator if provided
                    if (doubleTapLocatorType && doubleTapLocatorValue) {
                        action.locator_type = doubleTapLocatorType;
                        action.locator_value = doubleTapLocatorValue;
                    }

                    // Add fallback locators if any
                    if (fallbackLocators.length > 0) {
                        action.fallback_locators = fallbackLocators;
                        this.app.logAction('info', `Added ${fallbackLocators.length} fallback locators to double tap action`);
                    }

                    const doubleTapLocatorTimeout = parseInt(document.getElementById('doubleTapLocatorTimeout')?.value);
                    if (!isNaN(doubleTapLocatorTimeout) && doubleTapLocatorTimeout > 0) action.timeout = doubleTapLocatorTimeout;
                    const doubleTapLocatorInterval = parseFloat(document.getElementById('doubleTapLocatorInterval')?.value);
                    if (!isNaN(doubleTapLocatorInterval) && doubleTapLocatorInterval > 0) action.interval = doubleTapLocatorInterval;
                    action.method = 'locator';
                }
                else if (document.getElementById('doubletap-image').classList.contains('active') ||
                    document.getElementById('doubletap-image').classList.contains('show active')) {
                    // Using image
                    // Check if using text input or dropdown
                    const useTextInput = document.getElementById('doubleTapImageUseText').checked;
                    let imageFilename;
                    
                    if (useTextInput) {
                        imageFilename = document.getElementById('doubleTapImageTextInput').value.trim();
                    } else {
                        imageFilename = document.getElementById('doubleTapImageFilename').value;
                    }
                    
                    if (!imageFilename) {
                        actionValid = false;
                        this.app.logAction('error', 'Image is required for Double Tap by image');
                        break;
                    }
                    
                    // Always consider environment variables valid
                    if (this.isEnvironmentVariable(imageFilename)) {
                        actionValid = true;
                    }
                    action.image_filename = imageFilename;
                    action.threshold = parseFloat(document.getElementById('doubleTapThreshold').value) || 0.7;
                    action.timeout = parseInt(document.getElementById('doubleTapTimeout').value) || 20;
                    action.method = 'image';
                }
                else {
                    // Using coordinates
                    const doubleTapX = parseInt(document.getElementById('doubleTapX').value);
                    const doubleTapY = parseInt(document.getElementById('doubleTapY').value);
                    if (isNaN(doubleTapX) || isNaN(doubleTapY)) {
                        actionValid = false;
                        this.app.logAction('error', 'Invalid double tap coordinates');
                        break;
                    }
                    action.x = doubleTapX;
                    action.y = doubleTapY;
                    action.method = 'coordinates';
                }
                break;
            case 'launchApp':
                const packageId = document.getElementById('appPackage').value;
                if (!packageId) { actionValid = false; this.app.logAction('error', 'Package ID is required for Launch App'); break; }
                action.package_id = packageId;
                break;
            case 'restartApp':
                const restartPackageId = document.getElementById('restartPackage').value;
                if (!restartPackageId) { actionValid = false; this.app.logAction('error', 'Package ID is required for Restart App'); break; }
                action.package_id = restartPackageId;
                break;
            case 'terminateApp':
                const terminatePackageId = document.getElementById('terminatePackage').value;
                if (!terminatePackageId) { actionValid = false; this.app.logAction('error', 'Package ID is required for Terminate App'); break; }
                action.package_id = terminatePackageId;
                break;
            case 'uninstallApp':
                const uninstallPackageId = document.getElementById('uninstallPackage').value;
                if (!uninstallPackageId) { actionValid = false; this.app.logAction('error', 'Package ID is required for Uninstall App'); break; }
                action.package_id = uninstallPackageId;
                break;


            case 'deviceBack':
                // No parameters needed for device back action
                break;
            case 'getValue':
                const getValueLocatorType = document.getElementById('getValueLocatorType').value;
                const getValueLocatorValue = document.getElementById('getValueLocatorValue').value;
                const getValueAttribute = document.getElementById('getValueAttribute').value;
                const getValueTimeout = parseInt(document.getElementById('getValueTimeout').value);

                if (!getValueLocatorType || !getValueLocatorValue) {
                    actionValid = false;
                    this.app.logAction('error', 'Locator type and value are required for Get Value');
                    break;
                }

                action.locator_type = getValueLocatorType;
                action.locator_value = getValueLocatorValue;
                action.attribute = getValueAttribute;
                if (!isNaN(getValueTimeout) && getValueTimeout > 0) action.timeout = getValueTimeout;
                break;
            case 'compareValue':
                const compareValueLocatorType = document.getElementById('compareValueLocatorType').value;
                const compareValueLocatorValue = document.getElementById('compareValueLocatorValue').value;
                const compareValueAttribute = document.getElementById('compareValueAttribute').value;
                const compareValueExpected = document.getElementById('compareValueExpected').value;
                const compareValueTimeout = parseInt(document.getElementById('compareValueTimeout').value);

                if (!compareValueLocatorType || !compareValueLocatorValue) {
                    actionValid = false;
                    this.app.logAction('error', 'Locator type and value are required for Compare Value');
                    break;
                }

                if (compareValueExpected === undefined) {
                    actionValid = false;
                    this.app.logAction('error', 'Expected value is required for Compare Value');
                    break;
                }

                action.locator_type = compareValueLocatorType;
                action.locator_value = compareValueLocatorValue;
                action.attribute = compareValueAttribute;
                action.expected_value = compareValueExpected;
                if (!isNaN(compareValueTimeout) && compareValueTimeout > 0) action.timeout = compareValueTimeout;
                break;
            case 'getParam':
                const paramName = document.getElementById('getParamName').value;
                if (!paramName) {
                    actionValid = false;
                    this.app.logAction('error', 'Parameter name is required for Get Parameter');
                    break;
                }
                action.param_name = paramName;
                break;
            case 'setParam':
                const setParamName = document.getElementById('setParamName').value;
                const setParamValue = document.getElementById('setParamValue').value;
                if (!setParamName) {
                    actionValid = false;
                    this.app.logAction('error', 'Parameter name is required for Set Parameter');
                    break;
                }
                if (setParamValue === undefined || setParamValue === null) {
                    actionValid = false;
                    this.app.logAction('error', 'Parameter value is required for Set Parameter');
                    break;
                }
                action.param_name = setParamName;
                action.param_value = setParamValue;
                break;
            case 'exists':
                const existsLocatorType = document.getElementById('existsLocatorType').value;
                let existsLocatorValue = '';

                if (existsLocatorType === 'image') {
                    // Check if using environment variable
                    const useEnvVar = document.getElementById('existsUseEnvVar')?.checked;
                    if (useEnvVar) {
                        existsLocatorValue = document.getElementById('existsEnvVar')?.value;
                        if (!existsLocatorValue) {
                            actionValid = false;
                            this.app.logAction('error', 'Environment variable name is required when using environment variable for image');
                            break;
                        }
                        // Mark as environment variable
                        action.use_env_var = true;
                        action.env_var_name = existsLocatorValue;
                    } else {
                        existsLocatorValue = document.getElementById('existsImage').value;
                        if (!existsLocatorValue) {
                            actionValid = false;
                            this.app.logAction('error', 'Reference image is required for Check if Exists with Image locator');
                            break;
                        }
                    }

                    // Always consider environment variables valid
                    if (this.isEnvironmentVariable(existsLocatorValue)) {
                        actionValid = true;
                    }

                    // Add threshold for image matching
                    const existsThreshold = parseFloat(document.getElementById('existsThreshold')?.value);
                    if (!isNaN(existsThreshold)) {
                        action.threshold = existsThreshold;
                    }
                } else {
                    existsLocatorValue = document.getElementById('existsLocatorValue').value;
                    if (!existsLocatorValue) {
                        actionValid = false;
                        this.app.logAction('error', 'Locator value is required for Check if Exists');
                        break;
                    }
                }

                const existsTimeout = parseInt(document.getElementById('existsTimeout').value);

                action.locator_type = existsLocatorType;
                action.locator_value = existsLocatorValue;
                if (!isNaN(existsTimeout) && existsTimeout > 0) action.timeout = existsTimeout;
                break;
            case 'ifElseSteps':
                // Get condition settings
                const conditionType = document.getElementById('ifConditionType').value;
                action.condition_type = conditionType;

                // Process condition based on type
                switch (conditionType) {
                    case 'exists':
                        const ifExistsLocatorType = document.getElementById('ifExistsLocatorType').value;
                        let ifExistsLocatorValue = '';

                        if (ifExistsLocatorType === 'image') {
                            ifExistsLocatorValue = document.getElementById('ifExistsImage').value;
                            if (!ifExistsLocatorValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Reference image is required for If Exists condition with Image locator');
                                break;
                            }
                            
                            // Always consider environment variables valid
                            if (this.isEnvironmentVariable(ifExistsLocatorValue)) {
                                actionValid = true;
                            }
                        } else {
                            ifExistsLocatorValue = document.getElementById('ifExistsLocatorValue').value;
                            if (!ifExistsLocatorValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Locator value is required for If Exists condition');
                                break;
                            }
                        }

                        const ifExistsTimeout = parseInt(document.getElementById('ifExistsTimeout').value);

                        action.condition = {
                            locator_type: ifExistsLocatorType,
                            locator_value: ifExistsLocatorValue,
                            timeout: (!isNaN(ifExistsTimeout) && ifExistsTimeout > 0) ? ifExistsTimeout : 10
                        };
                        break;

                    case 'visible':
                        const ifVisibleLocatorType = document.getElementById('ifVisibleLocatorType').value;
                        const ifVisibleLocatorValue = document.getElementById('ifVisibleLocatorValue').value;

                        if (!ifVisibleLocatorType || !ifVisibleLocatorValue) {
                            actionValid = false;
                            this.app.logAction('error', 'Locator type and value are required for If Visible condition');
                            break;
                        }

                        const ifVisibleTimeout = parseInt(document.getElementById('ifVisibleTimeout').value);

                        action.condition = {
                            locator_type: ifVisibleLocatorType,
                            locator_value: ifVisibleLocatorValue,
                            timeout: (!isNaN(ifVisibleTimeout) && ifVisibleTimeout > 0) ? ifVisibleTimeout : 10
                        };
                        break;

                    case 'contains_text':
                        const ifContainsTextLocatorType = document.getElementById('ifContainsTextLocatorType').value;
                        const ifContainsTextLocatorValue = document.getElementById('ifContainsTextLocatorValue').value;
                        const ifContainsTextValue = document.getElementById('ifContainsTextValue').value;

                        if (!ifContainsTextLocatorType || !ifContainsTextLocatorValue) {
                            actionValid = false;
                            this.app.logAction('error', 'Element locator type and value are required for If Contains Text condition');
                            break;
                        }

                        if (!ifContainsTextValue) {
                            actionValid = false;
                            this.app.logAction('error', 'Text to check for is required for If Contains Text condition');
                            break;
                        }

                        const ifContainsTextTimeout = parseInt(document.getElementById('ifContainsTextTimeout').value);

                        action.condition = {
                            locator_type: ifContainsTextLocatorType,
                            locator_value: ifContainsTextLocatorValue,
                            text: ifContainsTextValue,
                            timeout: (!isNaN(ifContainsTextTimeout) && ifContainsTextTimeout > 0) ? ifContainsTextTimeout : 10
                        };
                        break;

                    case 'value_equals':
                        const ifValueEqualsLocatorType = document.getElementById('ifValueEqualsLocatorType').value;
                        const ifValueEqualsLocatorValue = document.getElementById('ifValueEqualsLocatorValue').value;
                        const ifValueEqualsExpectedValue = document.getElementById('ifValueEqualsExpectedValue').value;

                        if (!ifValueEqualsLocatorType || !ifValueEqualsLocatorValue) {
                            actionValid = false;
                            this.app.logAction('error', 'Element locator type and value are required for If Value Equals condition');
                            break;
                        }

                        if (!ifValueEqualsExpectedValue && ifValueEqualsExpectedValue !== '') {
                            actionValid = false;
                            this.app.logAction('error', 'Expected value is required for If Value Equals condition');
                            break;
                        }

                        const ifValueEqualsTimeout = parseInt(document.getElementById('ifValueEqualsTimeout').value);

                        action.condition = {
                            locator_type: ifValueEqualsLocatorType,
                            locator_value: ifValueEqualsLocatorValue,
                            expected_value: ifValueEqualsExpectedValue,
                            timeout: (!isNaN(ifValueEqualsTimeout) && ifValueEqualsTimeout > 0) ? ifValueEqualsTimeout : 10
                        };
                        break;

                    case 'value_contains':
                        const ifValueContainsLocatorType = document.getElementById('ifValueContainsLocatorType').value;
                        const ifValueContainsLocatorValue = document.getElementById('ifValueContainsLocatorValue').value;
                        const ifValueContainsExpectedValue = document.getElementById('ifValueContainsExpectedValue').value;

                        if (!ifValueContainsLocatorType || !ifValueContainsLocatorValue) {
                            actionValid = false;
                            this.app.logAction('error', 'Element locator type and value are required for If Value Contains condition');
                            break;
                        }

                        if (!ifValueContainsExpectedValue) {
                            actionValid = false;
                            this.app.logAction('error', 'Text to check for is required for If Value Contains condition');
                            break;
                        }

                        const ifValueContainsTimeout = parseInt(document.getElementById('ifValueContainsTimeout').value);

                        action.condition = {
                            locator_type: ifValueContainsLocatorType,
                            locator_value: ifValueContainsLocatorValue,
                            expected_value: ifValueContainsExpectedValue,
                            timeout: (!isNaN(ifValueContainsTimeout) && ifValueContainsTimeout > 0) ? ifValueContainsTimeout : 10
                        };
                        break;

                    case 'screen_contains':
                        const ifScreenContainsImage = document.getElementById('ifScreenContainsImage').value;

                        if (!ifScreenContainsImage) {
                            actionValid = false;
                            this.app.logAction('error', 'Reference image is required for If Screen Contains Image condition');
                            break;
                        }
                        
                        // Always consider environment variables valid
                        if (this.isEnvironmentVariable(ifScreenContainsImage)) {
                            actionValid = true;
                        }

                        const ifScreenContainsThreshold = parseFloat(document.getElementById('ifScreenContainsThreshold').value);
                        const ifScreenContainsTimeout = parseInt(document.getElementById('ifScreenContainsTimeout').value);

                        action.condition = {
                            image: ifScreenContainsImage,
                            threshold: (!isNaN(ifScreenContainsThreshold) && ifScreenContainsThreshold > 0 && ifScreenContainsThreshold <= 1) ? ifScreenContainsThreshold : 0.7,
                            timeout: (!isNaN(ifScreenContainsTimeout) && ifScreenContainsTimeout > 0) ? ifScreenContainsTimeout : 10
                        };
                        break;

                    default:
                        actionValid = false;
                        this.app.logAction('error', `Condition type '${conditionType}' is not supported`);
                        break;
                }

                if (!actionValid) break;

                // Get then action settings
                const thenActionType = document.getElementById('thenActionType').value;
                if (!thenActionType) {
                    actionValid = false;
                    this.app.logAction('error', 'Then action type must be selected');
                    break;
                }

                const thenAction = { type: thenActionType };

                // Populate then action based on selected type
                switch(thenActionType) {
                    case 'tap':
                        const thenTapX = parseInt(document.getElementById('then-tapX')?.value || 0);
                        const thenTapY = parseInt(document.getElementById('then-tapY')?.value || 0);
                        if (isNaN(thenTapX) || isNaN(thenTapY)) {
                            actionValid = false;
                            this.app.logAction('error', 'Invalid tap coordinates for Then action');
                            break;
                        }
                        thenAction.x = thenTapX;
                        thenAction.y = thenTapY;
                        break;

                    case 'doubleTap':
                        const thenDoubleTapX = parseInt(document.getElementById('then-doubleTapX')?.value || 0);
                        const thenDoubleTapY = parseInt(document.getElementById('then-doubleTapY')?.value || 0);
                        if (isNaN(thenDoubleTapX) || isNaN(thenDoubleTapY)) {
                            actionValid = false;
                            this.app.logAction('error', 'Invalid double tap coordinates for Then action');
                            break;
                        }
                        thenAction.x = thenDoubleTapX;
                        thenAction.y = thenDoubleTapY;
                        break;

                    case 'swipe':
                        const thenSwipeStartX = parseInt(document.getElementById('thenSwipeStartX').value);
                        const thenSwipeStartY = parseInt(document.getElementById('thenSwipeStartY').value);
                        const thenSwipeEndX = parseInt(document.getElementById('thenSwipeEndX').value);
                        const thenSwipeEndY = parseInt(document.getElementById('thenSwipeEndY').value);
                        const thenSwipeDuration = parseInt(document.getElementById('thenSwipeDuration').value);

                        if (isNaN(thenSwipeStartX) || isNaN(thenSwipeStartY) || isNaN(thenSwipeEndX) || isNaN(thenSwipeEndY)) {
                            actionValid = false;
                            this.app.logAction('error', 'Invalid swipe coordinates for Then action');
                            break;
                        }

                        thenAction.start_x = thenSwipeStartX;
                        thenAction.start_y = thenSwipeStartY;
                        thenAction.end_x = thenSwipeEndX;
                        thenAction.end_y = thenSwipeEndY;
                        thenAction.duration = (!isNaN(thenSwipeDuration) && thenSwipeDuration > 0) ? thenSwipeDuration : 300;
                        break;

                    case 'text':
                        const thenInputText = document.getElementById('then-inputText')?.value || '';
                        if (!thenInputText) {
                            actionValid = false;
                            this.app.logAction('error', 'Text input cannot be empty for Then action');
                            break;
                        }
                        thenAction.text = thenInputText;
                        break;

                    case 'key':
                        const thenKeyCode = document.getElementById('then-keyCode')?.value || '';
                        if (!thenKeyCode) {
                            actionValid = false;
                            this.app.logAction('error', 'Key code cannot be empty for Then action');
                            break;
                        }
                        thenAction.key = thenKeyCode;
                        break;

                    case 'wait':
                        const thenWaitTime = parseInt(document.getElementById('then-waitTime')?.value || 1);
                        if (isNaN(thenWaitTime) || thenWaitTime <= 0) {
                            actionValid = false;
                            this.app.logAction('error', 'Invalid wait time for Then action');
                            break;
                        }
                        thenAction.time = thenWaitTime;
                        break;



                    case 'clickElement':
                        const thenClickLocatorType = document.getElementById('thenClickElementLocatorType').value;
                        const thenClickLocatorValue = document.getElementById('thenClickElementLocator').value;
                        if (!thenClickLocatorType || !thenClickLocatorValue) {
                            actionValid = false;
                            this.app.logAction('error', 'Locator type and value are required for Then Click Element action');
                            break;
                        }
                        thenAction.locator_type = thenClickLocatorType;
                        thenAction.locator_value = thenClickLocatorValue;
                        const thenClickTimeout = parseInt(document.getElementById('thenClickElementTimeout').value);
                        if (!isNaN(thenClickTimeout) && thenClickTimeout > 0) thenAction.timeout = thenClickTimeout;
                        break;

                    case 'waitTill':
                        const thenWaitTillLocatorType = document.getElementById('thenWaitTillLocatorType').value;
                        const thenWaitTillLocatorValue = document.getElementById('thenWaitTillLocator').value;
                        if (!thenWaitTillLocatorType || !thenWaitTillLocatorValue) {
                            actionValid = false;
                            this.app.logAction('error', 'Locator type and value are required for Then Wait Till action');
                            break;
                        }
                        thenAction.locator_type = thenWaitTillLocatorType;
                        thenAction.locator_value = thenWaitTillLocatorValue;
                        const thenWaitTillTimeout = parseInt(document.getElementById('thenWaitTillTimeout').value);
                        if (!isNaN(thenWaitTillTimeout) && thenWaitTillTimeout > 0) thenAction.timeout = thenWaitTillTimeout;
                        break;

                    case 'swipeTillVisible':
                        const thenSwipeTVDirection = document.getElementById('thenSwipeTillVisibleDirection').value;
                        const thenSwipeTVCount = parseInt(document.getElementById('thenSwipeTillVisibleCount').value);

                        thenAction.direction = thenSwipeTVDirection;
                        thenAction.count = (!isNaN(thenSwipeTVCount) && thenSwipeTVCount > 0) ? thenSwipeTVCount : 5;
                        break;

                    case 'textClear':
                        const thenTextClearInput = document.getElementById('thenTextClearInput').value;
                        if (!thenTextClearInput) {
                            actionValid = false;
                            this.app.logAction('error', 'Text input cannot be empty for Then Clear & Input Text action');
                            break;
                        }
                        thenAction.text = thenTextClearInput;
                        const thenTextClearDelay = parseInt(document.getElementById('thenTextClearDelay').value);
                        if (!isNaN(thenTextClearDelay) && thenTextClearDelay > 0) thenAction.delay = thenTextClearDelay;
                        break;

                    case 'tapImage':
                        const thenTapImageFilename = document.getElementById('thenTapImageFilename').value;
                        if (!thenTapImageFilename) {
                            actionValid = false;
                            this.app.logAction('error', 'Reference image is required for Then Tap Image action');
                            break;
                        }
                        thenAction.image_filename = thenTapImageFilename;
                        thenAction.threshold = parseFloat(document.getElementById('thenTapImageThreshold').value) || 0.7;
                        thenAction.timeout = parseInt(document.getElementById('thenTapImageTimeout').value) || 20;
                        thenAction.method = 'image';
                        break;

                    case 'iosFunctions':
                        const thenIosFunction = document.getElementById('then-IosFunction')?.value || '';
                        if (!thenIosFunction) {
                            actionValid = false;
                            this.app.logAction('error', 'iOS function must be selected for Then action');
                            break;
                        }
                        thenAction.function_name = thenIosFunction;

                        // Add function-specific parameters
                        if (thenIosFunction === 'alert_click') {
                            const thenButtonText = document.getElementById('then-IosAlertButtonText')?.value || '';
                            if (!thenButtonText) {
                                actionValid = false;
                                this.app.logAction('error', 'Button text is required for Then alert_click function');
                                break;
                            }
                            thenAction.button_text = thenButtonText;
                        } else if (thenIosFunction === 'alert_wait') {
                            const thenTimeout = parseInt(document.getElementById('then-IosAlertTimeout')?.value || 2);
                            if (!isNaN(thenTimeout) && thenTimeout > 0) {
                                thenAction.timeout = thenTimeout;
                            } else {
                                thenAction.timeout = 2; // Default timeout
                            }
                        } else if (thenIosFunction === 'set_clipboard') {
                            const thenContent = document.getElementById('then-IosClipboardContent')?.value || '';
                            if (!thenContent) {
                                actionValid = false;
                                this.app.logAction('error', 'Content is required for Then set_clipboard function');
                                break;
                            }
                            thenAction.content = thenContent;
                        } else if (thenIosFunction === 'paste') {
                            // No additional parameters needed for paste function
                        } else if (thenIosFunction === 'text') {
                            const thenTextContent = document.getElementById('then-IosTextContent')?.value || '';
                            if (!thenTextContent) {
                                actionValid = false;
                                this.app.logAction('error', 'Text content is required for Then text function');
                                break;
                            }
                            thenAction.text = thenTextContent;

                            // Get the enter checkbox value
                            const thenEnterAfterText = document.getElementById('then-IosTextEnter')?.checked || false;
                            thenAction.enter = thenEnterAfterText;
                        }
                        break;

                    default:
                        actionValid = false;
                        this.app.logAction('error', `Then action type '${thenActionType}' is not supported`);
                        break;
                }

                if (!actionValid) break;

                action.then_action = thenAction;

                // Get else action settings (if available)
                const elseActionType = document.getElementById('elseActionType')?.value;

                // Only process else action if a type is selected
                if (elseActionType) {
                    const elseAction = { type: elseActionType };

                    // Populate else action based on selected type - similar to then action
                    switch(elseActionType) {
                        case 'tap':
                            const elseTapX = parseInt(document.getElementById('else-tapX')?.value || 0);
                            const elseTapY = parseInt(document.getElementById('else-tapY')?.value || 0);
                            if (isNaN(elseTapX) || isNaN(elseTapY)) {
                                actionValid = false;
                                this.app.logAction('error', 'Invalid tap coordinates for Else action');
                                break;
                            }
                            elseAction.x = elseTapX;
                            elseAction.y = elseTapY;
                            break;

                        case 'doubleTap':
                            const elseDoubleTapX = parseInt(document.getElementById('else-doubleTapX')?.value || 0);
                            const elseDoubleTapY = parseInt(document.getElementById('else-doubleTapY')?.value || 0);
                            if (isNaN(elseDoubleTapX) || isNaN(elseDoubleTapY)) {
                                actionValid = false;
                                this.app.logAction('error', 'Invalid double tap coordinates for Else action');
                                break;
                            }
                            elseAction.x = elseDoubleTapX;
                            elseAction.y = elseDoubleTapY;
                            break;

                        case 'swipe':
                            const elseSwipeStartX = parseInt(document.getElementById('elseSwipeStartX').value);
                            const elseSwipeStartY = parseInt(document.getElementById('elseSwipeStartY').value);
                            const elseSwipeEndX = parseInt(document.getElementById('elseSwipeEndX').value);
                            const elseSwipeEndY = parseInt(document.getElementById('elseSwipeEndY').value);
                            const elseSwipeDuration = parseInt(document.getElementById('elseSwipeDuration').value);

                            if (isNaN(elseSwipeStartX) || isNaN(elseSwipeStartY) || isNaN(elseSwipeEndX) || isNaN(elseSwipeEndY)) {
                                actionValid = false;
                                this.app.logAction('error', 'Invalid swipe coordinates for Else action');
                                break;
                            }

                            elseAction.start_x = elseSwipeStartX;
                            elseAction.start_y = elseSwipeStartY;
                            elseAction.end_x = elseSwipeEndX;
                            elseAction.end_y = elseSwipeEndY;
                            elseAction.duration = (!isNaN(elseSwipeDuration) && elseSwipeDuration > 0) ? elseSwipeDuration : 300;
                            break;

                        case 'text':
                            const elseInputText = document.getElementById('else-inputText')?.value || '';
                            if (!elseInputText) {
                                actionValid = false;
                                this.app.logAction('error', 'Text input cannot be empty for Else action');
                                break;
                            }
                            elseAction.text = elseInputText;
                            break;

                        case 'key':
                            const elseKeyCode = document.getElementById('else-keyCode')?.value || '';
                            if (!elseKeyCode) {
                                actionValid = false;
                                this.app.logAction('error', 'Key code cannot be empty for Else action');
                                break;
                            }
                            elseAction.key = elseKeyCode;
                            break;

                        case 'wait':
                            const elseWaitTime = parseInt(document.getElementById('else-waitTime')?.value || 1);
                            if (isNaN(elseWaitTime) || elseWaitTime <= 0) {
                                actionValid = false;
                                this.app.logAction('error', 'Invalid wait time for Else action');
                                break;
                            }
                            elseAction.time = elseWaitTime;
                            break;



                        case 'tapImage':
                            const elseTapImageFilename = document.getElementById('elseTapImageFilename').value;
                            if (!elseTapImageFilename) {
                                actionValid = false;
                                this.app.logAction('error', 'Reference image is required for Else Tap Image action');
                                break;
                            }
                            elseAction.image_filename = elseTapImageFilename;
                            elseAction.threshold = parseFloat(document.getElementById('elseTapImageThreshold').value) || 0.7;
                            elseAction.timeout = parseInt(document.getElementById('elseTapImageTimeout').value) || 20;
                            elseAction.method = 'image';
                            break;

                        case 'clickElement':
                            const elseClickLocatorType = document.getElementById('elseClickElementLocatorType').value;
                            const elseClickLocatorValue = document.getElementById('elseClickElementLocator').value;
                            if (!elseClickLocatorType || !elseClickLocatorValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Locator type and value are required for Else Click Element action');
                                break;
                            }
                            elseAction.locator_type = elseClickLocatorType;
                            elseAction.locator_value = elseClickLocatorValue;
                            const elseClickTimeout = parseInt(document.getElementById('elseClickElementTimeout').value);
                            if (!isNaN(elseClickTimeout) && elseClickTimeout > 0) elseAction.timeout = elseClickTimeout;
                            break;

                        case 'waitTill':
                            const elseWaitTillLocatorType = document.getElementById('elseWaitTillLocatorType').value;
                            const elseWaitTillLocatorValue = document.getElementById('elseWaitTillLocator').value;
                            if (!elseWaitTillLocatorType || !elseWaitTillLocatorValue) {
                                actionValid = false;
                                this.app.logAction('error', 'Locator type and value are required for Else Wait Till action');
                                break;
                            }
                            elseAction.locator_type = elseWaitTillLocatorType;
                            elseAction.locator_value = elseWaitTillLocatorValue;
                            const elseWaitTillTimeout = parseInt(document.getElementById('elseWaitTillTimeout').value);
                            if (!isNaN(elseWaitTillTimeout) && elseWaitTillTimeout > 0) elseAction.timeout = elseWaitTillTimeout;
                            break;

                        case 'swipeTillVisible':
                            const elseSwipeTVDirection = document.getElementById('elseSwipeTillVisibleDirection').value;
                            const elseSwipeTVCount = parseInt(document.getElementById('elseSwipeTillVisibleCount').value);

                            elseAction.direction = elseSwipeTVDirection;
                            elseAction.count = (!isNaN(elseSwipeTVCount) && elseSwipeTVCount > 0) ? elseSwipeTVCount : 5;
                            break;

                        case 'textClear':
                            const elseTextClearInput = document.getElementById('elseTextClearInput').value;
                            if (!elseTextClearInput) {
                                actionValid = false;
                                this.app.logAction('error', 'Text input cannot be empty for Else Clear & Input Text action');
                                break;
                            }
                            elseAction.text = elseTextClearInput;
                            const elseTextClearDelay = parseInt(document.getElementById('elseTextClearDelay').value);
                            if (!isNaN(elseTextClearDelay) && elseTextClearDelay > 0) elseAction.delay = elseTextClearDelay;
                            break;

                        case 'iosFunctions':
                            const elseIosFunction = document.getElementById('else-IosFunction')?.value || '';
                            if (!elseIosFunction) {
                                actionValid = false;
                                this.app.logAction('error', 'iOS function must be selected for Else action');
                                break;
                            }
                            elseAction.function_name = elseIosFunction;

                            // Add function-specific parameters
                            if (elseIosFunction === 'alert_click') {
                                const elseButtonText = document.getElementById('else-IosAlertButtonText')?.value || '';
                                if (!elseButtonText) {
                                    actionValid = false;
                                    this.app.logAction('error', 'Button text is required for Else alert_click function');
                                    break;
                                }
                                elseAction.button_text = elseButtonText;
                            } else if (elseIosFunction === 'alert_wait') {
                                const elseTimeout = parseInt(document.getElementById('else-IosAlertTimeout')?.value || 2);
                                if (!isNaN(elseTimeout) && elseTimeout > 0) {
                                    elseAction.timeout = elseTimeout;
                                } else {
                                    elseAction.timeout = 2; // Default timeout
                                }
                            } else if (elseIosFunction === 'set_clipboard') {
                                const elseContent = document.getElementById('else-IosClipboardContent')?.value || '';
                                if (!elseContent) {
                                    actionValid = false;
                                    this.app.logAction('error', 'Content is required for Else set_clipboard function');
                                    break;
                                }
                                elseAction.content = elseContent;
                            } else if (elseIosFunction === 'paste') {
                                // No additional parameters needed for paste function
                            } else if (elseIosFunction === 'text') {
                                const elseTextContent = document.getElementById('else-IosTextContent')?.value || '';
                                if (!elseTextContent) {
                                    actionValid = false;
                                    this.app.logAction('error', 'Text content is required for Else text function');
                                    break;
                                }
                                elseAction.text = elseTextContent;

                                // Get the enter checkbox value
                                const elseEnterAfterText = document.getElementById('else-IosTextEnter')?.checked || false;
                                elseAction.enter = elseEnterAfterText;
                            }
                            break;

                        default:
                            actionValid = false;
                            this.app.logAction('error', `Else action type '${elseActionType}' is not supported`);
                            break;
                    }

                    if (!actionValid) break;

                    // Add the else action to the updatedAction
                    updatedAction.else_action = elseAction;
                }
                break;
            case 'doubleClickImageAirtest':
                const imageName = document.getElementById('doubleClickAirtestImageFilename').value;
                if (!imageName) { actionValid = false; this.app.logAction('error', 'Image filename is required'); break; }
                updatedAction.image_filename = imageName;
                const threshold = parseFloat(document.getElementById('doubleClickAirtestThreshold')?.value);
                if (!isNaN(threshold) && threshold > 0 && threshold <= 1) updatedAction.threshold = threshold;
                const timeout = parseInt(document.getElementById('doubleClickAirtestTimeout')?.value);
                if (!isNaN(timeout) && timeout > 0) updatedAction.timeout = timeout;
                break;
            case 'doubleTap':
                // Check which tab is active
                if (document.getElementById('doubletap-locator').classList.contains('active') ||
                    document.getElementById('doubletap-locator').classList.contains('show active')) {
                    // Using locator
                    const doubleTapLocatorType = document.getElementById('doubleTapLocatorType').value;
                    const doubleTapLocatorValue = document.getElementById('doubleTapLocatorValue').value;

                    // Check if we have fallback locators
                    let fallbackLocators = [];
                    if (this.app.fallbackLocatorsManager) {
                        fallbackLocators = this.app.fallbackLocatorsManager.getFallbackLocators('doubleTap');
                    }

                    // If we have no primary locator but have fallbacks, that's okay
                    if ((!doubleTapLocatorType || !doubleTapLocatorValue) && fallbackLocators.length === 0) {
                        actionValid = false;
                        this.app.logAction('error', 'Locator type and value are required for Double Tap (or at least one fallback locator)');
                        break;
                    }

                    // Add primary locator if provided
                    if (doubleTapLocatorType && doubleTapLocatorValue) {
                        updatedAction.locator_type = doubleTapLocatorType;
                        updatedAction.locator_value = doubleTapLocatorValue;
                    }

                    // Add fallback locators if any
                    if (fallbackLocators.length > 0) {
                        updatedAction.fallback_locators = fallbackLocators;
                        this.app.logAction('info', `Added ${fallbackLocators.length} fallback locators to double tap action`);
                    }

                    const doubleTapLocatorTimeout = parseInt(document.getElementById('doubleTapLocatorTimeout')?.value);
                    if (!isNaN(doubleTapLocatorTimeout) && doubleTapLocatorTimeout > 0) updatedAction.timeout = doubleTapLocatorTimeout;
                    const doubleTapLocatorInterval = parseFloat(document.getElementById('doubleTapLocatorInterval')?.value);
                    if (!isNaN(doubleTapLocatorInterval) && doubleTapLocatorInterval > 0) updatedAction.interval = doubleTapLocatorInterval;
                    updatedAction.method = 'locator';
                }
                else if (document.getElementById('doubletap-image').classList.contains('active') ||
                    document.getElementById('doubletap-image').classList.contains('show active')) {
                    // Using image
                    const doubleTapImage = document.getElementById('doubleTapImageFilename').value;
                    if (!doubleTapImage) {
                        actionValid = false;
                        this.app.logAction('error', 'Image is required for Double Tap by image');
                        break;
                    }
                    updatedAction.image_filename = doubleTapImage;
                    updatedAction.threshold = parseFloat(document.getElementById('doubleTapThreshold').value) || 0.7;
                    updatedAction.timeout = parseInt(document.getElementById('doubleTapTimeout').value) || 20;
                    updatedAction.method = 'image';
                }
                else {
                    // Using coordinates
                    const doubleTapX = parseInt(document.getElementById('doubleTapX').value);
                    const doubleTapY = parseInt(document.getElementById('doubleTapY').value);
                    if (isNaN(doubleTapX) || isNaN(doubleTapY)) {
                        actionValid = false;
                        this.app.logAction('error', 'Invalid double tap coordinates');
                        break;
                    }
                    updatedAction.x = doubleTapX;
                    updatedAction.y = doubleTapY;
                    updatedAction.method = 'coordinates';
                }
                break;
            case 'swipeTillVisible':
                const swipeTVDirection = document.getElementById('swipeTillVisibleDirection').value;
                const startXTV = parseInt(document.getElementById('swipeTillVisibleStartX').value);
                const startYTV = parseInt(document.getElementById('swipeTillVisibleStartY').value);
                const endXTV = parseInt(document.getElementById('swipeTillVisibleEndX').value);
                const endYTV = parseInt(document.getElementById('swipeTillVisibleEndY').value);
                const durationTV = parseInt(document.getElementById('swipeTillVisibleDuration').value);
                const countTV = parseInt(document.getElementById('swipeTillVisibleCount').value) || 1;
                const intervalTV = parseFloat(document.getElementById('swipeTillVisibleInterval').value) || 0.5;
                let locatorType = document.getElementById('swipeTillVisibleLocatorType').value;
                let locatorValue = '';
                let textToFind = '';

                // Handle text locator type
                if (locatorType === 'text') {
                    textToFind = document.getElementById('swipeTillVisibleTextToFind').value;
                    if (!textToFind) {
                        actionValid = false;
                        this.app.logAction('error', 'Text to find is required for Swipe Till Visible with Text locator');
                        break;
                    }
                    updatedAction.text_to_find = textToFind;
                } else {
                    // For other locator types, get the locator value
                    locatorValue = document.getElementById('swipeTillVisibleLocatorValue').value;
                    if (!locatorValue && locatorType !== 'text') {
                        // Only require locator value if not using text or image
                        const referenceImage = document.getElementById('swipeTillVisibleReferenceImage').value;
                        if (!referenceImage || referenceImage.trim() === '') {
                            actionValid = false;
                            this.app.logAction('error', 'Either locator value or reference image is required for Swipe Till Visible');
                            break;
                        }
                    }
                    updatedAction.locator_value = locatorValue;
                }

                // Get reference image (optional)
                const referenceImage = document.getElementById('swipeTillVisibleReferenceImage').value;
                if (referenceImage && referenceImage.trim() !== '') {
                    updatedAction.image_filename = referenceImage;

                    // Add threshold and timeout for image recognition
                    const imageThreshold = parseFloat(document.getElementById('swipeTillVisibleThreshold')?.value);
                    if (!isNaN(imageThreshold) && imageThreshold > 0 && imageThreshold <= 1) {
                        updatedAction.threshold = imageThreshold;
                    } else {
                        updatedAction.threshold = 0.7; // Default
                    }

                    const imageTimeout = parseInt(document.getElementById('swipeTillVisibleImageTimeout')?.value);
                    if (!isNaN(imageTimeout) && imageTimeout > 0) {
                        updatedAction.timeout = imageTimeout;
                    } else {
                        updatedAction.timeout = 20; // Default
                    }
                }

                // Always include locator type
                updatedAction.locator_type = locatorType;

                if (isNaN(startXTV) || isNaN(startYTV) || isNaN(endXTV) || isNaN(endYTV)) {
                    actionValid = false;
                    this.app.logAction('error', 'Invalid swipeTillVisible coordinates');
                    break;
                }
                updatedAction.start_x = startXTV;
                updatedAction.start_y = startYTV;
                updatedAction.end_x = endXTV;
                updatedAction.end_y = endYTV;
                updatedAction.vector_start = [startXTV / 100, startYTV / 100];
                updatedAction.vector_end = [endXTV / 100, endYTV / 100];
                updatedAction.duration = (!isNaN(durationTV) && durationTV > 0) ? durationTV : 300;
                updatedAction.count = countTV;
                updatedAction.interval = intervalTV;
                updatedAction.direction = swipeTVDirection;
                break;
            case 'textClear':
                const textClearInput = document.getElementById('textClearInput').value;
                if (!textClearInput) {
                    actionValid = false;
                    this.app.logAction('error', 'Text input cannot be empty for Clear & Input Text');
                    break;
                }
                updatedAction.text = textClearInput;

                // Get delay between clear and input (milliseconds)
                const textClearDelay = parseInt(document.getElementById('textClearDelay').value);
                if (!isNaN(textClearDelay) && textClearDelay > 0) {
                    updatedAction.delay = textClearDelay;
                } else {
                    updatedAction.delay = 500; // Default delay
                }
                break;
            case 'iosFunctions':
                const iosFunction = document.getElementById('iosFunction').value;
                if (!iosFunction) {
                    actionValid = false;
                    this.app.logAction('error', 'iOS function must be selected');
                    break;
                }
                updatedAction.function_name = iosFunction;

                // Add function-specific parameters
                if (iosFunction === 'press') {
                    // Try both possible field IDs for key name

                } else if (iosFunction === 'alert_click') {
                    // Try both possible field IDs for button text
                    let buttonText = '';
                    if (document.getElementById('iosAlertButton')) {
                        buttonText = document.getElementById('iosAlertButton').value;
                    } else if (document.getElementById('iosAlertButtonText')) {
                        buttonText = document.getElementById('iosAlertButtonText').value;
                    }
                    
                    if (!buttonText) {
                        actionValid = false;
                        this.app.logAction('error', 'Button text is required for alert_click function');
                        break;
                    }
                    updatedAction.button_text = buttonText;
                } else if (iosFunction === 'alert_wait') {
                    if (document.getElementById('iosAlertTimeout')) {
                        const timeout = parseInt(document.getElementById('iosAlertTimeout').value);
                        if (!isNaN(timeout) && timeout > 0) {
                            updatedAction.timeout = timeout;
                        } else {
                            updatedAction.timeout = 2; // Default timeout
                        }
                    } else {
                        updatedAction.timeout = 2; // Default timeout
                    }
                } else if (iosFunction === 'text') {
                    // Try both possible field IDs for text input
                    let textInput = '';
                    let textFieldFound = false;
                    
                    if (document.getElementById('iosTextInput')) {
                        textInput = document.getElementById('iosTextInput').value;
                        textFieldFound = true;
                    } else if (document.getElementById('iosTextContent')) {
                        textInput = document.getElementById('iosTextContent').value;
                        textFieldFound = true;
                    }
                    
                    if (!textInput) {
                        actionValid = false;
                        this.app.logAction('error', 'Text input is required for text function');
                        break;
                    }
                    
                    updatedAction.text = textInput;
                    
                    // Get the enter checkbox value if it exists
                    if (document.getElementById('iosTextEnter')) {
                        updatedAction.enter = document.getElementById('iosTextEnter').checked;
                    } else {
                        updatedAction.enter = true; // Default to true
                    }
                } else if (iosFunction === 'set_clipboard') {
                    // Try both possible field IDs for clipboard content
                    let content = '';
                    if (document.getElementById('clipboardContent')) {
                        content = document.getElementById('clipboardContent').value;
                    } else if (document.getElementById('iosClipboardContent')) {
                        content = document.getElementById('iosClipboardContent').value;
                    }
                    
                    if (!content) {
                        actionValid = false;
                        this.app.logAction('error', 'Content is required for set_clipboard function');
                        break;
                    }
                    
                    updatedAction.content = content;
                } else if (iosFunction === 'paste') {
                    // No additional parameters needed for paste function

                }
                break;
            case 'multiStep':
                const testCaseId = document.getElementById('multiStepTestCase').value;
                if (!testCaseId) {
                    actionValid = false;
                    this.app.logAction('error', 'Please select a test case for Multi Step action');
                    break;
                }

                // Store the test case ID
                updatedAction.test_case_id = testCaseId;

                // Store the test case name and steps count for display
                const testCaseNameElement = document.getElementById('multiStepTestCaseName');
                const testCaseStepsElement = document.getElementById('multiStepTestCaseSteps');

                if (testCaseNameElement && testCaseStepsElement) {
                    updatedAction.test_case_name = testCaseNameElement.textContent;
                    updatedAction.test_case_steps_count = parseInt(testCaseStepsElement.textContent) || 0;
                }



                // Check if we have a MultiStepAction instance
                if (window.multiStepAction) {
                    // Try to pre-load the test case steps
                    const testCaseSelect = document.getElementById('multiStepTestCase');
                    if (testCaseSelect && testCaseSelect.dataset.loadedSteps) {
                        try {
                            const steps = JSON.parse(testCaseSelect.dataset.loadedSteps);
                            if (steps && Array.isArray(steps)) {
                                updatedAction.test_case_steps = steps;
                                updatedAction.steps_loaded = true;
                                this.app.logAction('info', `Pre-loaded ${steps.length} steps for Multi Step action`);
                            }
                        } catch (e) {
                            console.error('Error parsing pre-loaded steps:', e);
                        }
                    }
                }

                // We'll load the actual test case steps when executing if not pre-loaded
                updatedAction.expanded = false; // Initially collapsed
                break;

            case 'repeatSteps':
                const repeatCount = document.getElementById('repeatCount').value;
                const repeatTestCaseId = document.getElementById('repeatStepsTestCase').value;
                const repeatDelay = document.getElementById('repeatDelay').value;

                if (!repeatTestCaseId) {
                    actionValid = false;
                    this.app.logAction('error', 'Please select a test case for Repeat Steps action');
                    break;
                }

                if (!repeatCount || parseInt(repeatCount) < 1 || parseInt(repeatCount) > 100) {
                    actionValid = false;
                    this.app.logAction('error', 'Repeat count must be between 1 and 100');
                    break;
                }

                // Store the repeat parameters
                updatedAction.repeat_count = parseInt(repeatCount);
                updatedAction.test_case_id = repeatTestCaseId;
                updatedAction.repeat_delay = parseFloat(repeatDelay) || 0;

                // Store the test case name for display
                const repeatTestCaseSelect = document.getElementById('repeatStepsTestCase');
                const selectedOption = repeatTestCaseSelect.selectedOptions[0];
                if (selectedOption && selectedOption.dataset.testCase) {
                    try {
                        const testCase = JSON.parse(selectedOption.dataset.testCase);
                        updatedAction.test_case_name = testCase.name;
                        updatedAction.test_case_steps = testCase.actions || [];
                        updatedAction.test_case_steps_count = testCase.actions ? testCase.actions.length : 0;
                    } catch (e) {
                        console.error('Error parsing test case data:', e);
                        updatedAction.test_case_name = repeatTestCaseId;
                    }
                } else {
                    updatedAction.test_case_name = repeatTestCaseId;
                }

                this.app.logAction('info', `Added Repeat Steps action: ${repeatCount}x ${updatedAction.test_case_name}`);
                break;

            case 'hookAction':
                // Use the HookAction class to get the hook action data
                if (window.hookAction) {
                    if (!window.hookAction.validateHookAction()) {
                        actionValid = false;
                        break;
                    }

                    const hookActionData = window.hookAction.getHookActionData();
                    if (!hookActionData) {
                        actionValid = false;
                        this.app.logAction('error', 'Failed to get hook action data');
                        break;
                    }

                    // Copy the hook action data to the updated action
                    updatedAction.hook_type = hookActionData.hook_type;
                    updatedAction.hook_data = hookActionData.hook_data;

                    this.app.logAction('info', `Added Hook Action of type: ${updatedAction.hook_type}`);
                } else {
                    actionValid = false;
                    this.app.logAction('error', 'Hook Action module not loaded');
                }
                break;

            case 'tapAndType':
                const tapAndTypeText = document.getElementById('tapAndTypeText').value;
                const tapAndTypeTimeout = parseInt(document.getElementById('tapAndTypeTimeout').value);

                if (!tapAndTypeText) {
                    actionValid = false;
                    this.app.logAction('error', 'Text to input is required for Tap and Type');
                    break;
                }

                // Check which tab is active to determine the method
                const locatorTab = document.getElementById('tapAndType-locator-tab');
                const coordinatesTab = document.getElementById('tapAndType-coordinates-tab');

                // Default to locator method if we can't determine
                let usingLocatorMethod = true;

                // Check if coordinates tab is active
                if (coordinatesTab && coordinatesTab.classList.contains('active')) {
                    usingLocatorMethod = false;
                }

                console.log('Tap and Type method detection:', {
                    locatorTabActive: locatorTab?.classList.contains('active'),
                    coordinatesTabActive: coordinatesTab?.classList.contains('active'),
                    usingLocatorMethod: usingLocatorMethod
                });

                if (usingLocatorMethod) {
                    // Using locator method
                    const tapAndTypeLocatorType = document.getElementById('tapAndTypeLocatorType').value;
                    const tapAndTypeLocatorValue = document.getElementById('tapAndTypeLocatorValue').value;

                    if (!tapAndTypeLocatorType || !tapAndTypeLocatorValue) {
                        actionValid = false;
                        this.app.logAction('error', 'Locator type and value are required for Tap and Type with locator method');
                        break;
                    }

                    action.method = 'locator';
                    action.locator_type = tapAndTypeLocatorType;
                    action.locator_value = tapAndTypeLocatorValue;
                } else {
                    // Using coordinates method
                    const tapAndTypeXInput = document.getElementById('tapAndTypeX').value;
                    const tapAndTypeYInput = document.getElementById('tapAndTypeY').value;

                    // Check if values are environment variables (env[...])
                    const isXEnvVar = this.isEnvironmentVariable(tapAndTypeXInput);
                    const isYEnvVar = this.isEnvironmentVariable(tapAndTypeYInput);

                    // If either coordinate is an environment variable, keep it as a string
                    // Otherwise parse as integer
                    const tapAndTypeX = isXEnvVar ? tapAndTypeXInput : parseInt(tapAndTypeXInput);
                    const tapAndTypeY = isYEnvVar ? tapAndTypeYInput : parseInt(tapAndTypeYInput);

                    if ((isXEnvVar || !isNaN(tapAndTypeX)) && (isYEnvVar || !isNaN(tapAndTypeY))) {
                        action.method = 'coordinates';
                        action.x = tapAndTypeX;
                        action.y = tapAndTypeY;
                    } else {
                        actionValid = false;
                        this.app.logAction('error', 'Valid X and Y coordinates or environment variables are required for Tap and Type with coordinates method');
                        break;
                    }
                }

                action.text = tapAndTypeText;
                if (!isNaN(tapAndTypeTimeout) && tapAndTypeTimeout > 0) action.timeout = tapAndTypeTimeout;

                // Get the selected data generator
                const tapAndTypeElement = document.getElementById('tapAndTypeText');
                if (tapAndTypeElement && tapAndTypeElement.dataset.generatorId && tapAndTypeElement.dataset.generatorId !== 'none') {
                    action.data_generator = tapAndTypeElement.dataset.generatorId;
                    this.app.logAction('info', `Text will be generated using: ${tapAndTypeElement.dataset.generatorId}`);
                }
                break;

            case 'takeScreenshot':
                const screenshotName = document.getElementById('screenshotName').value;
                if (!screenshotName) {
                    actionValid = false;
                    this.app.logAction('error', 'Screenshot name is required for Take Screenshot action');
                    break;
                }

                // No validation during action addition - validation should be done via the validate button
                action.screenshot_name = screenshotName;
                this.app.logAction('info', `Added Take Screenshot action: ${screenshotName}`);
                break;

             // Add other action types as needed
             default:
                console.warn(`ActionManager: Add logic not fully implemented for action type: ${selectedType}`);
                actionValid = false; // Mark as invalid if type not fully handled
                this.app.logAction('error', `Action type '${selectedType}' is not fully configured for adding.`);
                break;
        }

        if (actionValid) {
            // Add to the main app's current actions array
            this.app.currentActions.push(action);

            // Add to the UI list (uses internal method)
            this.addActionToList(action);

            // Reset the form (calls main app method)
            this.app.actionFormManager.resetActionForm(); // CORRECTED LINE

            // Update execution buttons state (calls main app method)
            this.app.updateExecutionButtons();

            this.app.logAction('success', `Added ${selectedType} action`);
        } else {
            this.app.logAction('warning', `Could not add ${selectedType} action due to invalid form data.`);
        }
    }

    /**
     * Add an action to the list at a specific position
     * @param {Object} actionData - The action object to add
     * @param {number} position - The position to insert the action at (0-based index)
     */
    addActionToListAtPosition(actionData, position) {
        try {
            if (!actionData || !actionData.type) {
                console.error('Invalid action object:', actionData);
                return;
            }

            // Validate position
            if (position < 0 || position > this.app.currentActions.length) {
                console.error(`Invalid position for insertion: ${position}`);
                this.app.logAction('error', `Invalid position for insertion: ${position}`);
                return;
            }

            // Create the action item
            const actionItem = this.createActionItem(actionData, position);

            // Insert at the specified position
            if (this.actionsList) {
                // If position is at the end, just append
                if (position >= this.actionsList.children.length) {
                    this.actionsList.appendChild(actionItem);
                } else {
                    // Otherwise, insert before the element at the specified position
                    this.actionsList.insertBefore(actionItem, this.actionsList.children[position]);
                }

                // Update all action indices after the insertion point
                this.app.updateStepNumbers();

                // Log action added
                this.app.logAction('success', `Added action at position ${position + 1}`);

                // Update execution buttons
                this.app.updateExecutionButtons();
            } else {
                console.error("ActionManager: Could not find actionsList element.");
                this.app.logAction('error', 'UI Error: Could not find the action list element.');
            }
        } catch (error) {
            console.error("ActionManager Error in addActionToListAtPosition:", error);
            this.app.logAction('error', `Failed to add action to UI at position ${position}: ${error.message}`);
        }
    }

    /**
     * Create an action item element
     * @param {Object} actionData - The action data
     * @param {number} actionIndex - The index of the action
     * @returns {HTMLElement} - The created action item element
     */
    createActionItem(actionData, actionIndex) {
        const stepNumber = actionIndex + 1;

        const actionItem = document.createElement('div');
        actionItem.className = 'list-group-item d-flex justify-content-between align-items-center action-item';
        actionItem.dataset.actionIndex = actionIndex.toString();

        // Use the app's getActionDescription method
        let actionText = this.getActionDescription(actionData);

        // Check if this is a Multi Step action
        const isMultiStep = actionData.type === 'multiStep';

        // Check if this is a Hook Action
        const isHookAction = actionData.type === 'hookAction';



        // Add special styling for Hook Actions
        if (isHookAction) {
            actionItem.classList.add('hook-action-item');
            actionItem.style.backgroundColor = '#f8f9fa'; // Light gray background
            actionItem.style.borderLeft = '4px solid #6c757d'; // Gray left border
        } else {
            actionItem.classList.remove('hook-action-item');
            actionItem.style.backgroundColor = '';
            actionItem.style.borderLeft = '';
        }

        // Create the action item with proper step number badge
        actionItem.innerHTML = `
            <div class="action-content">
                <i class="bi bi-grip-vertical drag-indicator me-2" style="cursor:grab"></i>
                <span class="badge bg-secondary step-number me-2">${stepNumber}</span>
                <span class="badge ${isHookAction ? 'bg-secondary' : 'bg-primary'} me-2">${actionData.type}</span>
                ${isHookAction ? '<span class="badge bg-warning text-dark me-2" title="This action will only be executed when a step fails">Recovery Action</span>' : ''}

                <span class="action-text">${isHookAction && actionData.hook_type === 'tap' && actionData.hook_data ?
                    `Hook Action: ${actionData.hook_type} (${
                        actionData.hook_data.method === 'locator' && actionData.hook_data.locator_type && actionData.hook_data.locator_value ?
                            `tap on element with ${actionData.hook_data.locator_type}: '${actionData.hook_data.locator_value}'` :
                        actionData.hook_data.method === 'image' && actionData.hook_data.image_filename ?
                            `tap on image '${actionData.hook_data.image_filename}'` :
                        actionData.hook_data.x !== undefined && actionData.hook_data.y !== undefined ?
                            `tap at coordinates (${actionData.hook_data.x}, ${actionData.hook_data.y})` :
                        'tap'
                    })` :
                    actionText
                }</span>
                ${isMultiStep ? `
                    <button class="btn btn-sm btn-outline-secondary ms-2 toggle-multi-step" title="Expand/Collapse">
                        <i class="bi bi-chevron-down"></i>
                    </button>
                ` : ''}
            </div>
            <div class="action-buttons">
                <button class="btn btn-sm btn-outline-primary play-action me-1" title="${isHookAction ? 'Hook Actions are only executed when a step fails' : 'Play this action'}" ${isHookAction ? 'disabled' : ''}>
                    <i class="bi bi-play-fill"></i>
                </button>
                <button class="btn btn-sm btn-outline-secondary duplicate-action me-1" title="Duplicate this action">
                    <i class="bi bi-files"></i>
                </button>
                <button class="btn btn-sm btn-outline-secondary edit-action me-1" title="Edit this action">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger delete-action" title="Delete this action">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `;

        // If this is a Multi Step action, add the container for steps
        if (isMultiStep) {
            const multiStepContainer = document.createElement('div');
            multiStepContainer.className = 'multi-step-container mt-2 ps-4 border-start';
            multiStepContainer.style.display = 'none'; // Initially hidden

            // Add loading indicator
            multiStepContainer.innerHTML = `
                <div class="text-center py-2">
                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                    <span class="ms-2">Loading test case steps...</span>
                </div>
            `;

            actionItem.appendChild(multiStepContainer);

            // Add event listener for the toggle button
            const toggleButton = actionItem.querySelector('.toggle-multi-step');
            if (toggleButton) {
                toggleButton.addEventListener('click', (e) => {
                    e.stopPropagation(); // Prevent event bubbling

                    // Toggle the container visibility
                    if (multiStepContainer.style.display === 'none') {
                        multiStepContainer.style.display = 'block';
                        toggleButton.querySelector('i').classList.replace('bi-chevron-down', 'bi-chevron-up');

                        // Load the test case steps if not already loaded
                        if (!actionData.steps_loaded) {
                            this.loadMultiStepTestCaseSteps(actionData, multiStepContainer);
                        }
                    } else {
                        multiStepContainer.style.display = 'none';
                        toggleButton.querySelector('i').classList.replace('bi-chevron-up', 'bi-chevron-down');
                    }
                });
            }
        }

        // Add event listeners to the buttons
        const playButton = actionItem.querySelector('.play-action');
        if (playButton) {
            // Disable play button for Hook Actions
            if (actionData.type === 'hookAction') {
                playButton.disabled = true;
                playButton.title = 'Hook Actions are only executed when a step fails';
            } else {
                playButton.disabled = false;
                playButton.title = 'Play this action';
            }

            playButton.addEventListener('click', () => {
                // Skip if this is a Hook Action
                if (actionData.type === 'hookAction') {
                    this.app.logAction('info', 'Hook Actions are only executed when a step fails');
                    return;
                }

                // Get the current index from the DOM element
                const currentIndex = Array.from(this.actionsList.children).indexOf(actionItem);
                console.log(`Play button clicked for action at index ${currentIndex}`);

                if (currentIndex >= 0 && currentIndex < this.app.currentActions.length) {
                    this.app.playAction(this.app.currentActions[currentIndex], currentIndex);
                } else {
                    console.error(`Invalid action index: ${currentIndex}`);
                    this.app.logAction('error', 'Invalid action index for playing');
                }
            });
        }

        // Add duplicate button event listener
        const duplicateButton = actionItem.querySelector('.duplicate-action');
        if (duplicateButton) {
            duplicateButton.addEventListener('click', () => {
                // Get the current index from the DOM element
                const currentIndex = Array.from(this.actionsList.children).indexOf(actionItem);
                console.log(`Duplicate button clicked for action at index ${currentIndex}`);

                if (currentIndex >= 0 && currentIndex < this.app.currentActions.length) {
                    this.app.duplicateAction(currentIndex);
                } else {
                    console.error(`Invalid action index: ${currentIndex}`);
                    this.app.logAction('error', 'Invalid action index for duplication');
                }
            });
        }

        const editButton = actionItem.querySelector('.edit-action');
        if (editButton) {
            editButton.addEventListener('click', () => {
                // Get the current index from the DOM element
                const currentIndex = Array.from(this.actionsList.children).indexOf(actionItem);
                console.log(`Edit button clicked for action at index ${currentIndex}`);

                if (currentIndex >= 0 && currentIndex < this.app.currentActions.length) {
                    this.app.editAction(this.app.currentActions[currentIndex], currentIndex);
                } else {
                    console.error(`Invalid action index: ${currentIndex}`);
                    this.app.logAction('error', 'Invalid action index for editing');
                }
            });
        }

        const deleteButton = actionItem.querySelector('.delete-action');
        if (deleteButton) {
            deleteButton.addEventListener('click', () => {
                // Get the current index from the DOM element
                const currentIndex = Array.from(this.actionsList.children).indexOf(actionItem);
                console.log(`Delete button clicked for action at index ${currentIndex}`);

                if (currentIndex >= 0 && currentIndex < this.app.currentActions.length) {
                    if (confirm('Delete this action?')) {
                        this.app.currentActions.splice(currentIndex, 1);
                        actionItem.remove();
                        this.app.logAction('info', `Action removed at index ${currentIndex}`);
                        this.app.updateStepNumbers();
                        this.app.updateExecutionButtons();
                    }
                } else {
                    console.error(`Invalid action index: ${currentIndex}`);
                    this.app.logAction('error', 'Invalid action index for deleting');
                }
            });
        }

        return actionItem;
    }

    addActionToList(actionData) {
        try {
            // Get the index for the new action (which is now the last in the app's array)
            const actionIndex = this.app.currentActions.length - 1;

            // Create the action item using our helper method
            const actionItem = this.createActionItem(actionData, actionIndex);

            // Set disabled state styling if action is disabled
            if (actionData.enabled === false) {
                actionItem.classList.add('action-disabled');
            }

            // Add to list
            if (this.actionsList) {
                this.actionsList.appendChild(actionItem);
            } else {
                 console.error("ActionManager: Could not find actionsList element.");
                 this.app.logAction('error', 'UI Error: Could not find the action list element.');
                 return; // Stop if UI element is missing
            }

            // Log action added
            this.app.logAction('success', `Added action: ${actionData.type}`);

            // Enable execution buttons if there are actions (calls main app method)
            this.app.updateExecutionButtons();

            // Save to server (Optional - consider if this should be triggered elsewhere)
            // this.app.saveActionsToServer();
        } catch (error) {
            console.error("ActionManager Error in addActionToList:", error);
            this.app.logAction('error', `Failed to add action to UI: ${error.message}`);
        }
    }

    /**
     * Load the test case steps for a Multi Step action
     * @param {Object} action - The Multi Step action
     * @param {HTMLElement} container - The container to display the steps
     */
    loadMultiStepTestCaseSteps(action, container) {
        if (!action || !action.test_case_id || !container) {
            console.error('Invalid parameters for loadMultiStepTestCaseSteps');
            return;
        }

        // Show loading indicator
        container.innerHTML = `
            <div class="text-center py-2">
                <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                <span class="ms-2">Loading test case steps...</span>
            </div>
        `;

        // Fetch the test case steps from the server
        fetch(`/api/test_cases/load/${action.test_case_id}`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.test_case) {
                    // Store the steps in the action object
                    action.test_case_steps = data.test_case.actions || [];
                    action.steps_loaded = true;

                    // Display the steps
                    this.displayMultiStepTestCaseSteps(action, container);
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            Failed to load test case steps: ${data.error || 'Unknown error'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error loading test case steps:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        Error loading test case steps: ${error.message}
                    </div>
                `;
            });
    }

    /**
     * Display the test case steps for a Multi Step action
     * @param {Object} action - The Multi Step action
     * @param {HTMLElement} container - The container to display the steps
     */
    displayMultiStepTestCaseSteps(action, container) {
        if (!action || !action.test_case_steps || !container) {
            console.error('Invalid parameters for displayMultiStepTestCaseSteps');
            return;
        }

        // Clear the container
        container.innerHTML = '';

        if (action.test_case_steps.length === 0) {
            container.innerHTML = `
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    No steps found in the test case
                </div>
            `;
            return;
        }

        // Create a list of steps
        const stepsList = document.createElement('div');
        stepsList.className = 'multi-step-list';

        // Add each step to the list
        action.test_case_steps.forEach((step, index) => {
            const stepItem = document.createElement('div');
            stepItem.className = 'multi-step-item d-flex justify-content-between align-items-center mb-1';
            stepItem.dataset.stepIndex = index;

            // Create step content
            const stepContent = document.createElement('div');
            stepContent.className = 'multi-step-content';
            stepContent.innerHTML = `
                <span class="badge bg-secondary me-2">${index + 1}</span>
                <span class="badge bg-primary me-2">${step.type}</span>
                ${this.getActionDescription(step)}
            `;

            // Create status indicator for multi-step items
            const stepStatus = document.createElement('div');
            stepStatus.className = 'multi-step-status ms-auto';
            
            // Add to step item
            stepItem.appendChild(stepContent);
            stepItem.appendChild(stepStatus);

            // Add to list
            stepsList.appendChild(stepItem);
        });

        // Add the list to the container
        container.appendChild(stepsList);
    }

    // Helper to update a specific action item in the UI after an edit
    updateActionItemUI(index, updatedActionData) {
        // Find the action item in the DOM using its position in the list
        const actionItem = this.actionsList.children[index];
        if (!actionItem) {
            this.app.logAction('error', `Could not find action item UI for index ${index} to update.`);
            return;
        }

        const actionText = this.getActionDescription(updatedActionData);
        const stepNumber = index + 1;
        const isMultiStep = updatedActionData.type === 'multiStep';

        const stepBadge = actionItem.querySelector('.step-number');
        const typeBadge = actionItem.querySelector('.badge.bg-primary');
        const contentDiv = actionItem.querySelector('.action-content');

        if (stepBadge) stepBadge.textContent = stepNumber;
        if (typeBadge) typeBadge.textContent = updatedActionData.type;

        if (contentDiv) {
            // Remove old text node if exists
            let textNodeFound = false;
            contentDiv.childNodes.forEach(node => {
                if (node.nodeType === Node.TEXT_NODE || (node.nodeName === 'SPAN' && !node.classList.contains('badge'))) {
                    contentDiv.removeChild(node);
                    textNodeFound = true;
                }
            });
             if (!textNodeFound) { // Fallback if node wasn't found above
                 while(contentDiv.childNodes.length > 3) {
                     if(contentDiv.lastChild.nodeType === Node.TEXT_NODE || contentDiv.lastChild.nodeName === 'SPAN' && !contentDiv.lastChild.classList.contains('badge')) {
                         contentDiv.removeChild(contentDiv.lastChild);
                     } else { break; }
                 }
             }
            // Add new text node
            contentDiv.appendChild(document.createTextNode(` ${actionText}`));

            // Add expand/collapse button for Multi Step actions
            if (isMultiStep) {
                const toggleButton = contentDiv.querySelector('.toggle-multi-step');
                if (!toggleButton) {
                    const toggleBtn = document.createElement('button');
                    toggleBtn.className = 'btn btn-sm btn-outline-secondary ms-2 toggle-multi-step';
                    toggleBtn.title = 'Expand/Collapse';
                    toggleBtn.innerHTML = '<i class="bi bi-chevron-down"></i>';
                    contentDiv.appendChild(toggleBtn);

                    // Add event listener for the toggle button
                    toggleBtn.addEventListener('click', (e) => {
                        e.stopPropagation(); // Prevent event bubbling

                        // Find or create the container
                        let multiStepContainer = actionItem.querySelector('.multi-step-container');
                        if (!multiStepContainer) {
                            multiStepContainer = document.createElement('div');
                            multiStepContainer.className = 'multi-step-container mt-2 ps-4 border-start';
                            multiStepContainer.style.display = 'none'; // Initially hidden
                            actionItem.appendChild(multiStepContainer);
                        }

                        // Toggle the container visibility
                        if (multiStepContainer.style.display === 'none') {
                            multiStepContainer.style.display = 'block';
                            toggleBtn.querySelector('i').classList.replace('bi-chevron-down', 'bi-chevron-up');

                            // Load the test case steps if not already loaded
                            if (!updatedActionData.steps_loaded) {
                                this.loadMultiStepTestCaseSteps(updatedActionData, multiStepContainer);
                            }
                        } else {
                            multiStepContainer.style.display = 'none';
                            toggleBtn.querySelector('i').classList.replace('bi-chevron-up', 'bi-chevron-down');
                        }
                    });
                }
            } else {
                // Remove expand/collapse button if not a Multi Step action
                const toggleButton = contentDiv.querySelector('.toggle-multi-step');
                if (toggleButton) {
                    contentDiv.removeChild(toggleButton);
                }

                // Remove multi-step container if exists
                const multiStepContainer = actionItem.querySelector('.multi-step-container');
                if (multiStepContainer) {
                    actionItem.removeChild(multiStepContainer);
                }
            }
        }

        // Re-attach listeners (important as innerHTML was likely modified indirectly)
        // Play
        const playBtn = actionItem.querySelector('.play-action');
        const newPlayBtn = playBtn.cloneNode(true); // Clone to remove old listeners
        playBtn.parentNode.replaceChild(newPlayBtn, playBtn);
        newPlayBtn.addEventListener('click', () => {
            // Get the current index from the DOM element
            const currentIndex = Array.from(this.actionsList.children).indexOf(actionItem);
            console.log(`Play button clicked for updated action at index ${currentIndex}`);

            if (currentIndex >= 0 && currentIndex < this.app.currentActions.length) {
                this.app.playAction(this.app.currentActions[currentIndex], currentIndex);
            } else {
                console.error(`Invalid action index: ${currentIndex}`);
                this.app.logAction('error', 'Invalid action index for playing');
            }
        });

        // Duplicate
        const duplicateBtn = actionItem.querySelector('.duplicate-action');
        if (duplicateBtn) {
            const newDuplicateBtn = duplicateBtn.cloneNode(true);
            duplicateBtn.parentNode.replaceChild(newDuplicateBtn, duplicateBtn);
            newDuplicateBtn.addEventListener('click', () => {
                // Get the current index from the DOM element
                const currentIndex = Array.from(this.actionsList.children).indexOf(actionItem);
                console.log(`Duplicate button clicked for updated action at index ${currentIndex}`);

                if (currentIndex >= 0 && currentIndex < this.app.currentActions.length) {
                    this.app.duplicateAction(currentIndex);
                } else {
                    console.error(`Invalid action index: ${currentIndex}`);
                    this.app.logAction('error', 'Invalid action index for duplication');
                }
            });
        } else {
            // If duplicate button doesn't exist, add it
            const actionButtons = actionItem.querySelector('.action-buttons');
            if (actionButtons) {
                const editBtn = actionItem.querySelector('.edit-action');
                if (editBtn) {
                    const duplicateBtn = document.createElement('button');
                    duplicateBtn.className = 'btn btn-sm btn-outline-secondary duplicate-action me-1';
                    duplicateBtn.title = 'Duplicate this action';
                    duplicateBtn.innerHTML = '<i class="bi bi-files"></i>';

                    // Add event listener
                    duplicateBtn.addEventListener('click', () => {
                        const currentIndex = Array.from(this.actionsList.children).indexOf(actionItem);
                        if (currentIndex >= 0 && currentIndex < this.app.currentActions.length) {
                            this.app.duplicateAction(currentIndex);
                        } else {
                            console.error(`Invalid action index: ${currentIndex}`);
                            this.app.logAction('error', 'Invalid action index for duplication');
                        }
                    });

                    // Insert before edit button
                    actionButtons.insertBefore(duplicateBtn, editBtn);
                }
            }
        }

        // Edit
        const editBtn = actionItem.querySelector('.edit-action');
        const newEditBtn = editBtn.cloneNode(true);
        editBtn.parentNode.replaceChild(newEditBtn, editBtn);
        newEditBtn.addEventListener('click', () => {
            // Get the current index from the DOM element
            const currentIndex = Array.from(this.actionsList.children).indexOf(actionItem);
            console.log(`Edit button clicked for updated action at index ${currentIndex}`);

            if (currentIndex >= 0 && currentIndex < this.app.currentActions.length) {
                this.app.editAction(this.app.currentActions[currentIndex], currentIndex);
            } else {
                console.error(`Invalid action index: ${currentIndex}`);
                this.app.logAction('error', 'Invalid action index for editing');
            }
        });

        // Delete (listener logic remains complex, refers to data from app array)
        const deleteBtn = actionItem.querySelector('.delete-action');
        const newDeleteBtn = deleteBtn.cloneNode(true);
        deleteBtn.parentNode.replaceChild(newDeleteBtn, deleteBtn);
        if (newDeleteBtn) {
            newDeleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                if (confirm('Delete this action?')) {
                    // Get the current index from the DOM element
                    const currentIndex = Array.from(this.actionsList.children).indexOf(actionItem);
                    console.log(`Delete button clicked for updated action at index ${currentIndex}`);

                    if (currentIndex >= 0 && currentIndex < this.app.currentActions.length) {
                        this.app.currentActions.splice(currentIndex, 1);
                        actionItem.remove();
                        this.app.logAction('info', `Action removed at index ${currentIndex}`);
                        this.app.updateStepNumbers();
                        this.app.updateExecutionButtons();
                    } else {
                        console.error(`Invalid action index: ${currentIndex}`);
                        this.app.logAction('error', 'Invalid action index for deleting');
                    }
                }
            });
        }
    }
}

// Make the class available globally
window.ActionManager = ActionManager;