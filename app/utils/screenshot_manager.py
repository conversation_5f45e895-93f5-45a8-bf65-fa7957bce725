import os
import logging
import shutil
import glob
import time
import base64
from datetime import datetime
import traceback

logger = logging.getLogger(__name__)

class ScreenshotManager:
    """
    Manages screenshots for test execution, saving them directly to the report folder
    """

    def __init__(self):
        self.report_dir = None
        self.screenshots_dir = None
        self.initialized = False

    def initialize(self, report_dir=None, timestamp=None):
        """
        Initialize the screenshot manager with a report directory

        Args:
            report_dir (str, optional): Path to the report directory. If None, a new one will be created.
            timestamp (str, optional): Timestamp to use for the report directory. If None, current time will be used.

        Returns:
            tuple: (report_dir, screenshots_dir) - Paths to the report and screenshots directories
        """
        try:
            # If report_dir is provided, use it
            if report_dir:
                self.report_dir = report_dir
            else:
                # Generate timestamp for the report if not provided
                if not timestamp:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                # Get the reports directory path from the database
                try:
                    from utils.directory_paths_db import directory_paths_db
                    reports_dir = directory_paths_db.get_path('REPORTS')
                    if not reports_dir:
                        # No fallback - reports directory must be configured in Settings tab
                        raise Exception("Reports directory not configured in Settings tab")

                    # Ensure absolute path
                    if not os.path.isabs(reports_dir):
                        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                        reports_dir = os.path.join(base_dir, reports_dir)

                    logger.info(f"Using reports directory from Settings tab: {reports_dir}")
                except Exception as e:
                    logger.error(f"Error getting reports directory from Settings: {str(e)}")
                    raise Exception("Reports directory not configured in Settings tab. Please configure it before running tests.")

                # Ensure reports directory exists
                os.makedirs(reports_dir, exist_ok=True)
                logger.info(f"Using reports directory from settings: {reports_dir}")

                # Create the main report directory with timestamp
                self.report_dir = os.path.join(reports_dir, f'testsuite_execution_{timestamp}')

            # Create the report directory if it doesn't exist
            os.makedirs(self.report_dir, exist_ok=True)
            logger.info(f"Created/ensured report directory: {self.report_dir}")

            # Create the screenshots subdirectory
            self.screenshots_dir = os.path.join(self.report_dir, 'screenshots')
            os.makedirs(self.screenshots_dir, exist_ok=True)
            logger.info(f"Created/ensured screenshots directory: {self.screenshots_dir}")

            self.initialized = True
            return self.report_dir, self.screenshots_dir
        except Exception as e:
            logger.error(f"Error initializing screenshot manager: {str(e)}")
            traceback.print_exc()
            return None, None

    def save_screenshot(self, screenshot_data=None, filename=None, action_id=None, device_controller=None):
        """
        Save a screenshot to the report screenshots directory

        Args:
            screenshot_data (bytes, optional): Screenshot data to save. If None, a new screenshot will be taken.
            filename (str, optional): Filename to use. If None, action_id will be used.
            action_id (str, optional): Action ID to use for the filename. If None, a random one will be generated.
            device_controller (AppiumDeviceController, optional): Device controller to use for taking screenshots.

        Returns:
            str or None: Path to the saved screenshot or None if failed
        """
        if not self.initialized:
            logger.error("Screenshot manager not initialized")
            return None

        try:
            # Generate a random action_id if not provided
            if not action_id:
                import random
                import string
                chars = string.ascii_letters + string.digits
                action_id = ''.join(random.choice(chars) for _ in range(10))
                logger.info(f"Generated random action_id: {action_id}")

            # Always use action_id for the filename, regardless of provided filename
            filename = f"{action_id}.png"
            logger.info(f"Using action_id for screenshot filename: {filename}")

            # Create the full path
            screenshot_path = os.path.join(self.screenshots_dir, filename)

            # If screenshot_data is provided, save it directly
            if screenshot_data:
                # If screenshot_data is base64 encoded, decode it
                if isinstance(screenshot_data, str) and screenshot_data.startswith('data:image/png;base64,'):
                    screenshot_data = base64.b64decode(screenshot_data.split(',')[1])

                # Save the screenshot data
                with open(screenshot_path, 'wb') as f:
                    f.write(screenshot_data)
                logger.info(f"Saved screenshot data to: {screenshot_path}")
                return screenshot_path

            # If device_controller is provided, use it to take a screenshot
            elif device_controller:
                # Take the screenshot with the exact filename
                screenshot_result = device_controller.take_screenshot(filename=screenshot_path)

                # Check if the screenshot was taken successfully
                if isinstance(screenshot_result, dict) and screenshot_result.get('status') == 'success':
                    logger.info(f"Saved screenshot to: {screenshot_path}")
                    return screenshot_path
                elif os.path.exists(screenshot_path):
                    logger.info(f"Screenshot exists at: {screenshot_path}")
                    return screenshot_path
                else:
                    logger.error(f"Failed to take screenshot: {screenshot_result}")
                    return None
            else:
                logger.error("No screenshot data or device controller provided")
                return None
        except Exception as e:
            logger.error(f"Error saving screenshot: {str(e)}")
            traceback.print_exc()
            return None

# Create a global instance
screenshot_manager = ScreenshotManager()
